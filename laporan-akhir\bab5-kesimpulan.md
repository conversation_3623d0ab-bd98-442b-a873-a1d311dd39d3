# BAB V

# KESIMPULAN

## 5.1 Kesimpulan Masalah: Faktor-faktor Signifikan dalam Prediksi Risiko Fatigue

Penelitian ini berhasil menjawab pertanyaan penelitian pertama mengenai "Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat divalidasi melalui ablation study" melalui analisis SHAP yang komprehensif dan systematic ablation study.

Berdasarkan SHAP ablation study yang komprehensif, faktor-faktor yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa adalah fitur-fitur linguistik yang berkaitan dengan cara mahasiswa mendeskripsikan aktivitas mereka. Pomokit_unique_words dengan kontribusi 5.54% menjadi prediktor terkuat, mencerminkan diversitas linguistik dalam deskripsi aktivitas produktivitas. Total_title_diversity dengan kontribusi 5.33% menunjukkan variasi dalam cara mengkategorisasi aktivitas, sementara title_balance_ratio dengan kontribusi 5.19% mencerminkan keseimbangan deskripsi antara aktivitas produktivitas dan fisik.

Temuan yang mengejutkan adalah bahwa fitur-fitur temporal seperti avg_time_minutes (4.73%) dan total_time_minutes (4.02%) menempati posisi keempat dan kelima, menunjukkan bahwa aspek linguistik lebih dominan dibandingkan aspek kuantitatif dalam prediksi fatigue. Hal ini mengindikasikan bahwa cara mahasiswa mengekspresikan dan mendeskripsikan aktivitas mereka mengandung informasi yang lebih kaya tentang kondisi psikologis dan kognitif yang berkaitan dengan fatigue.

Kontribusi individual setiap fitur berhasil divalidasi melalui systematic ablation study yang menunjukkan konsistensi luar biasa. Top 5 fitur menunjukkan konsistensi 100% across semua algoritma (Logistic Regression, Random Forest, Gradient Boosting, XGBoost), dengan variance yang sangat rendah (0.0006-0.0011) mengkonfirmasi robustness temuan ini. SHAP-based feature selection terbukti superior dibandingkan random selection, dengan SHAP features memberikan keuntungan yang konsisten: Logistic Regression (+0.17%), Random Forest (+0.98%), dan Gradient Boosting (+1.91%). Hanya XGBoost yang menunjukkan penurunan (-1.48%), mengindikasikan bahwa model yang terlalu kompleks dapat mengalami degradasi performa ketika menggunakan subset fitur yang optimal.

Ablation study juga mengungkap bahwa fitur-fitur linguistik memiliki SHAP consensus score tertinggi: pomokit_unique_words (19.8), total_title_diversity (19.2), dan title_balance_ratio (18.7), memvalidasi dominasi aspek linguistik dalam prediksi fatigue mahasiswa. Dominasi fitur linguistik mengungkap insight fundamental bahwa diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mencerminkan pola kognitif yang berkaitan dengan kondisi fatigue. Mahasiswa yang menggunakan vocabulary yang lebih beragam, memiliki variasi dalam mengkategorisasi aktivitas, dan menunjukkan keseimbangan dalam mendeskripsikan aktivitas produktivitas dan fisik, menunjukkan pola fatigue yang dapat diprediksi dengan akurasi tinggi.

Temuan ini memiliki implikasi praktis yang signifikan untuk pengembangan sistem monitoring fatigue. Sistem dapat fokus pada analisis pola linguistik dalam self-reporting aktivitas sebagai early warning system, yang lebih praktis dan less intrusive dibandingkan monitoring data fisiologis yang kompleks.

## 5.2 Kesimpulan Metode: Efektivitas Model Machine Learning

Penelitian ini berhasil menjawab pertanyaan penelitian kedua mengenai "Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas" melalui evaluasi komprehensif menggunakan dual evaluation strategy dan analisis overfitting yang mendalam.

Efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue menunjukkan hasil yang realistis dan dapat diimplementasikan. XGBoost mencapai performa terbaik dengan test accuracy 79.66% dan F1-score 79.54%, menunjukkan kemampuan superior dalam menangkap pola kompleks dalam data aktivitas kardiovaskular dan produktivitas mahasiswa. Namun, evaluasi yang lebih mendalam mengungkap bahwa XGBoost menunjukkan risiko overfitting yang tinggi dengan CV accuracy hanya 66.76% dan overfitting score 22.01. Hal ini mengindikasikan bahwa meskipun XGBoost memberikan akurasi tertinggi pada test set, generalisasinya terbatas untuk data baru.

Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test accuracy (71.19%) dan CV performance (69.35%), serta overfitting score terendah (9.23). Meskipun akurasinya lebih rendah, Logistic Regression memberikan prediksi yang lebih reliable dan konsisten, menjadikannya pilihan yang lebih suitable untuk deployment jangka panjang.

Evaluasi komprehensif mengungkap bahwa efektivitas model machine learning sangat dipengaruhi oleh karakteristik dataset. Untuk dataset berukuran sedang (291 samples) dengan distribusi kelas yang tidak seimbang (high_risk hanya 7.9%), model linear memberikan generalisasi yang lebih baik dibandingkan model kompleks. Model tree-based (Random Forest, Gradient Boosting, XGBoost) menunjukkan overfitting yang signifikan dengan overfitting score lebih dari 21 dan train-validation gap lebih dari 27%. Hal ini mengindikasikan bahwa kompleksitas model harus disesuaikan dengan ukuran dataset untuk mencapai efektivitas optimal.

Dual evaluation strategy yang mengombinasikan train-test split untuk SHAP analysis dan k-fold cross-validation untuk robustness evaluation terbukti efektif dalam mengidentifikasi model yang truly effective. Perbedaan signifikan antara hasil train-test split (79.66%) dan cross-validation (66.76%) mengkonfirmasi pentingnya evaluasi yang komprehensif.

Penelitian ini mengembangkan metodologi komprehensif untuk meningkatkan efektivitas model machine learning. Bias Correction Framework melalui `BiasCorrectTitleClassifier` berhasil menghasilkan target variable `corrected_fatigue_risk` yang lebih robust dan mengurangi risiko data leakage. SHAP-based feature selection terbukti meningkatkan efektivitas model dengan mengidentifikasi fitur yang benar-benar informatif. Pendekatan ini memberikan interpretabilitas yang superior dibandingkan metode tradisional dan memungkinkan stakeholder memahami reasoning di balik prediksi. Multi-modal data integration melalui weekly aggregation dengan feature engineering menghasilkan 20 fitur informatif yang mencakup aspek produktivitas, aktivitas fisik, temporal, dan linguistik. Integrasi ini memungkinkan model menangkap pola kompleks dari berbagai modalitas data aktivitas mahasiswa.

## 5.3 Kesimpulan Eksperimen: Analisis Berbasis Judul Aktivitas

Penelitian ini berhasil menjawab pertanyaan penelitian ketiga mengenai "Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap" melalui eksperimen komprehensif yang memvalidasi efektivitas pendekatan title-based analysis.

Analisis berbasis judul aktivitas (title-only analysis) terbukti dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap. Fitur-fitur linguistik yang diekstrak dari judul aktivitas mendominasi ranking feature importance dengan konsistensi tinggi across semua algoritma. Eksperimen menunjukkan bahwa tiga fitur linguistik teratas (pomokit_unique_words, total_title_diversity, title_balance_ratio) berkontribusi total 15.06% dari seluruh feature importance, lebih tinggi dibandingkan kombinasi fitur kuantitatif lainnya. Hal ini memvalidasi hipotesis bahwa informasi yang terkandung dalam judul aktivitas sudah cukup untuk prediksi fatigue yang akurat.

Perbandingan dengan fitur kuantitatif menunjukkan bahwa fitur temporal (avg_time_minutes, total_time_minutes) hanya berkontribusi 8.75%, sementara fitur produktivitas dan aktivitas fisik berkontribusi lebih rendah lagi. Temuan ini mengkonfirmasi bahwa title-only analysis dapat menjadi alternatif yang viable untuk prediksi fatigue.

Title-only analysis memiliki beberapa keunggulan signifikan dibandingkan pendekatan yang memerlukan data kuantitatif lengkap. Pertama, pendekatan ini lebih praktis dan less intrusive karena tidak memerlukan tracking data fisiologis yang detail seperti heart rate, durasi aktivitas yang presisi, atau metrics kuantitatif lainnya yang mungkin mengganggu aktivitas normal mahasiswa. Kedua, title-only analysis dapat diimplementasikan dengan lebih mudah dalam sistem monitoring yang existing, karena sebagian besar aplikasi aktivitas sudah memiliki fitur untuk input judul atau deskripsi aktivitas. Hal ini mengurangi barrier untuk adoption dan implementasi sistem monitoring fatigue. Ketiga, pendekatan ini memberikan insights yang lebih kaya tentang aspek psikologis dan kognitif mahasiswa. Pola linguistik dalam deskripsi aktivitas mencerminkan state of mind, tingkat engagement, dan awareness mahasiswa terhadap aktivitas mereka, yang berkorelasi kuat dengan kondisi fatigue.

Eksperimen komprehensif memvalidasi efektivitas title-only analysis melalui berbagai metrik evaluasi. Konsistensi fitur linguistik across semua algoritma (100% consistency rate) menunjukkan robustness pendekatan ini. SHAP consensus score yang tinggi untuk fitur linguistik (19.8, 19.2, 18.7) mengkonfirmasi bahwa informasi dalam judul aktivitas memang informatif untuk prediksi fatigue. Perbandingan SHAP features vs random features menunjukkan bahwa fitur linguistik memberikan kontribusi yang signifikan dan bukan hasil dari chance. Improvement yang konsisten pada sebagian besar algoritma memvalidasi bahwa title-only analysis dapat diandalkan untuk prediksi fatigue yang akurat.

Penelitian ini memberikan kontribusi signifikan terhadap bidang health informatics dan machine learning melalui beberapa aspek utama. Kontribusi pertama adalah novel insight berupa identifikasi fitur linguistik sebagai prediktor utama fatigue risk, yang membuka perspektif baru dalam memahami hubungan antara pola linguistik dan kondisi kesehatan mental mahasiswa. Kontribusi kedua adalah methodological contribution melalui pengembangan framework bias correction dan dual evaluation strategy yang dapat diadaptasi untuk penelitian serupa di domain lain. Framework ini memberikan pendekatan yang sistematis untuk mengatasi bias dalam data dan evaluasi model yang komprehensif. Kontribusi ketiga berupa practical application dengan memberikan rekomendasi konkret untuk implementasi sistem monitoring fatigue yang dapat diaplikasikan dalam konteks nyata monitoring kesehatan mahasiswa. Rekomendasi ini mencakup pemilihan model, feature engineering, dan strategi deployment yang praktis. Kontribusi keempat adalah overfitting awareness melalui demonstrasi pentingnya model selection yang sesuai dengan dataset size, memberikan guidance yang valuable untuk peneliti dalam memilih kompleksitas model yang tepat berdasarkan karakteristik data yang tersedia.

Secara keseluruhan, penelitian ini berhasil mengembangkan metodologi yang robust untuk prediksi risiko fatigue mahasiswa dengan akurasi yang realistis dan interpretabilitas yang tinggi, memberikan foundation yang solid untuk pengembangan sistem monitoring kesehatan mahasiswa yang lebih efektif dan dapat diimplementasikan dalam praktik.
