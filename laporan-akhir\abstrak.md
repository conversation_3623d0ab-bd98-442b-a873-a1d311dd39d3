# ABSTRAK

Penelitian ini mengembangkan sistem klasifikasi risiko fatigue mahasiswa menggunakan SHAP feature selection pada data aktivitas kardiovaskular dan produktivitas. Dataset terdiri dari 291 observasi mingguan dengan 20 fitur yang dianalisis menggunakan empat algoritma machine learning (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) dengan implementasi bias correction framework. Hasil menunjukkan fitur linguistik mendominasi prediksi fatigue, dengan pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%) sebagai prediktor terkuat. XGBoost mencapai akurasi terbaik 79.66% namun berisiko overfitting, sementara Logistic Regression menunjukkan stabilitas superior (71.19% test accuracy, 69.35% CV accuracy). Analisis berbasis judul aktivitas terbukti efektif dengan kontribusi fitur linguistik 15.06% dari total feature importance. SHAP feature selection superior dibandingkan random selection dengan peningkatan 0.17%-1.91%. Kontribusi utama penelitian adalah identifikasi fitur linguistik sebagai prediktor dominan fatigue, pengembangan framework bias correction, dan validasi title-only analysis untuk monitoring fatigue yang praktis dan non-intrusive.

**Kata kunci:** klasifikasi fatigue, SHAP feature selection, machine learning, fitur linguistik, title-only analysis, bias correction
