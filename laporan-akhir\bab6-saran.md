# BAB VI

# SARAN

## 6.1 Saran untuk Penelitian Lanjutan

1. **Data Augmentation**: Menggunakan teknik SMOTE atau GAN untuk memperbesar dataset (>500 samples) agar model tree-based dapat bekerja optimal tanpa overfitting
2. **Analisis <PERSON>udinal**: Mengeksplorasi pola fatigue sepanjang semester akademik dan mengidentifikasi periode kritis (ujian, deadline)
3. **Multi-Modal Feature Engineering**: Mengintegrasikan data tambahan seperti pola tidur, aktivitas sosial media, dan sensor smartphone
4. **Real-Time Implementation**: Mengembangkan sistem monitoring kontinyu dengan NLP advanced untuk sentiment dan emotion detection

## 6.2 Saran untuk Implementasi Praktis

1. **Model Selection**: Menggunakan Logistic Regression sebagai model utama (stabilitas) dengan XGBoost sebagai pendukung (akurasi maksimal) dalam arsitektur hybrid
2. **User Interface**: Mengembangkan mobile app atau web dashboard yang terintegrasi dengan aplikasi existing mahasiswa, user-friendly dan non-intrusive
3. **Privacy Protection**: Implementasi federated learning atau differential privacy untuk melindungi data sensitif mahasiswa
4. **Proactive Intervention**: Sistem alert dengan personalized recommendations dan resources untuk stress management sebelum fatigue mencapai level berbahaya

## 6.3 Saran untuk Pengembangan Metodologi

Metodologi penelitian yang dikembangkan dalam studi ini dapat diperbaiki dan diperluas untuk meningkatkan robustness dan generalizability hasil.

Pengembangan framework bias correction yang lebih sophisticated dapat meningkatkan kualitas data dan akurasi prediksi. Framework dapat diperluas untuk mendeteksi dan mengoreksi berbagai jenis bias seperti selection bias, confirmation bias, dan temporal bias yang mungkin terdapat dalam self-reported data aktivitas mahasiswa.

Eksplorasi teknik ensemble learning yang menggabungkan kekuatan berbagai algoritma dapat memberikan performa yang lebih optimal. Teknik seperti stacking, blending, atau voting ensemble dapat digunakan untuk menggabungkan prediksi dari Logistic Regression (untuk stabilitas) dan XGBoost (untuk akurasi) dalam satu sistem yang robust.

Pengembangan metrik evaluasi yang lebih comprehensive dan domain-specific untuk fatigue prediction dapat memberikan assessment yang lebih akurat tentang performa model. Metrik dapat mencakup aspek seperti early warning capability, false positive rate dalam konteks kesehatan mental, dan practical utility dalam setting akademik.

Implementasi cross-domain validation dengan dataset dari institusi atau populasi yang berbeda dapat meningkatkan generalizability temuan. Penelitian dapat mengeksplorasi bagaimana model yang dikembangkan pada satu populasi mahasiswa dapat diadaptasi untuk populasi lain dengan karakteristik demografis atau akademik yang berbeda.

Pengembangan interpretability techniques yang lebih advanced dapat meningkatkan trust dan adoption sistem. Selain SHAP, teknik seperti LIME, attention mechanisms, atau counterfactual explanations dapat digunakan untuk memberikan explanations yang lebih intuitive dan actionable bagi end users.

Secara keseluruhan, saran-saran ini bertujuan untuk meningkatkan akurasi, praktikalitas, dan impact dari sistem prediksi risiko fatigue mahasiswa, sehingga dapat memberikan kontribusi yang signifikan untuk kesehatan dan kesejahteraan mahasiswa dalam lingkungan akademik.
