# BAB V

# KESIMPULAN

## 5.1 Jawaban Pertanyaan Penelitian

Penelitian ini berhasil menjawab ketiga pertanyaan penelitian yang diidentifikasi dalam Bab 1 melalui eksperimen komprehensif dan analisis mendalam.

### 5.1.1 Faktor-faktor Signifikan dalam Prediksi Risiko Fatigue

Berdasarkan SHAP ablation study yang komprehensif, faktor-faktor yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa adalah fitur-fitur linguistik yang berkaitan dengan cara mahasiswa mendeskripsikan aktivitas mereka. Pomokit_unique_words dengan kontribusi 5.54% menjadi prediktor terkuat, diikuti oleh total_title_diversity (5.33%) dan title_balance_ratio (5.19%). Kontribusi individual setiap fitur berhasil divalidasi melalui ablation study yang menunjukkan konsistensi 100% across semua algoritma untuk top 5 fitur, dengan SHAP features member<PERSON><PERSON> keuntung<PERSON> 0.17% hingga 1.91% dibandingkan random selection.

Temuan ini mengungkap bahwa diversitas linguistik dalam deskripsi aktivitas mencerminkan pola kognitif yang berkaitan dengan kondisi fatigue mahasiswa. Mahasiswa yang menggunakan vocabulary yang lebih beragam dan memiliki keseimbangan dalam mendeskripsikan aktivitas produktivitas dan fisik menunjukkan pola fatigue yang dapat diprediksi dengan akurasi tinggi.

### 5.1.2 Efektivitas Model Machine Learning

Efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas menunjukkan hasil yang realistis dan dapat diimplementasikan. XGBoost mencapai performa terbaik dengan test accuracy 79.66% dan F1-score 79.54%, namun menunjukkan risiko overfitting dengan CV accuracy 66.76%. Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test (71.19%) dan CV performance (69.35%), menjadikannya pilihan yang lebih reliable untuk deployment jangka panjang.

Evaluasi komprehensif mengungkap bahwa untuk dataset berukuran sedang (291 samples), model linear memberikan generalisasi yang lebih baik dibandingkan model kompleks. Model tree-based menunjukkan overfitting score lebih dari 21 dengan train-validation gap lebih dari 27%, mengindikasikan bahwa kompleksitas model harus disesuaikan dengan ukuran dataset.

### 5.1.3 Analisis Berbasis Judul Aktivitas

Analisis berbasis judul aktivitas (title-only analysis) terbukti dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap. Fitur-fitur linguistik yang diekstrak dari judul aktivitas (pomokit_unique_words, total_title_diversity, title_balance_ratio) mendominasi ranking feature importance dan menunjukkan konsistensi tinggi across semua algoritma.

Hasil ini memvalidasi hipotesis bahwa pola linguistik dalam self-reporting aktivitas mengandung informasi yang kaya tentang kondisi psikologis dan kognitif mahasiswa. Title-based analysis memberikan pendekatan yang lebih praktis dan less intrusive untuk monitoring fatigue, karena tidak memerlukan tracking data kuantitatif yang detail seperti heart rate, durasi aktivitas, atau metrics fisiologis lainnya.

## 5.2 Kesimpulan Masalah

Penelitian ini berhasil mengidentifikasi dan mengatasi masalah utama dalam prediksi risiko fatigue mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Masalah yang dihadapi meliputi kompleksitas dalam mengintegrasikan data heterogen dari berbagai sumber, ketidakseimbangan distribusi kelas dalam dataset, dan kebutuhan akan interpretabilitas model untuk aplikasi praktis dalam monitoring kesehatan mahasiswa.

### 5.1.1 Identifikasi Masalah Utama

Masalah pertama yang berhasil diidentifikasi adalah kompleksitas integrasi data multi-modal, dimana dataset terdiri dari data aktivitas fisik (Strava) dan data produktivitas akademik (Pomokit) yang memiliki karakteristik temporal dan struktural yang berbeda. Integrasi kedua jenis data ini memerlukan pendekatan khusus untuk mempertahankan informasi temporal sambil menciptakan representasi yang konsisten untuk machine learning.

Masalah kedua berkaitan dengan ketidakseimbangan distribusi kelas dalam dataset. Distribusi target variable menunjukkan ketidakseimbangan yang signifikan dengan high_risk hanya 7.9% (23 observasi), medium_risk 52.6% (153 observasi), dan low_risk 39.5% (115 observasi) dari total 291 samples. Ketidakseimbangan ini berpotensi menyebabkan bias dalam prediksi dan memerlukan strategi khusus dalam modeling.

Masalah ketiga adalah potensi bias dalam klasifikasi fatigue, dimana proses klasifikasi fatigue risk berpotensi mengandung bias yang berasal dari subjektivitas dalam labeling atau korelasi yang tidak diinginkan dengan fitur-fitur tertentu. Hal ini memerlukan implementasi bias correction untuk memastikan validitas prediksi.

Masalah keempat berkaitan dengan kebutuhan interpretabilitas model untuk aplikasi dalam monitoring kesehatan mahasiswa. Model tidak hanya harus akurat tetapi juga interpretable, sehingga stakeholder dapat memahami faktor-faktor apa yang berkontribusi terhadap prediksi risiko fatigue untuk dapat mengambil tindakan preventif yang tepat.

### 5.2.2 Dampak Masalah Terhadap Prediksi Fatigue

Masalah-masalah yang diidentifikasi memiliki dampak signifikan terhadap akurasi dan reliabilitas prediksi fatigue. Integrasi data yang tidak optimal dapat menghasilkan noise yang mengurangi signal-to-noise ratio, sehingga model kesulitan mengekstrak pola yang bermakna dari data. Ketidakseimbangan kelas dan dataset berukuran sedang (291 samples) meningkatkan risiko overfitting, dimana model cenderung menghafal pola spesifik dari training data daripada belajar generalisasi yang baik.

Bias dalam data dapat diamplifikasi oleh model machine learning, sehingga prediksi yang dihasilkan tidak mencerminkan kondisi sebenarnya dari risiko fatigue mahasiswa. Model yang tidak interpretable juga sulit diimplementasikan dalam praktik monitoring kesehatan karena stakeholder tidak dapat memahami reasoning di balik prediksi yang diberikan.

### 5.2.3 Validasi Masalah Melalui Eksperimen

Eksperimen yang dilakukan berhasil memvalidasi keberadaan masalah-masalah tersebut melalui bukti empiris. Model tree-based menunjukkan overfitting score lebih dari 21 dengan train-validation gap lebih dari 27%, mengkonfirmasi bahwa kompleksitas model tidak sesuai dengan ukuran dataset. Tanpa SHAP analysis, sulit mengidentifikasi fitur yang benar-benar informatif untuk prediksi fatigue, sehingga model cenderung menggunakan fitur yang tidak relevan atau bahkan misleading.

Perbedaan signifikan antara hasil train-test split (79.66%) dan cross-validation (66.76%) mengkonfirmasi masalah generalisasi, dimana model menunjukkan performa yang overoptimistic pada pembagian data tertentu namun tidak konsisten ketika dievaluasi dengan metode yang lebih robust.

## 5.3 Kesimpulan Metode

Penelitian ini mengembangkan metodologi komprehensif yang menggabungkan bias correction, SHAP-based feature selection, dan dual evaluation strategy untuk mengatasi masalah-masalah yang diidentifikasi. Metodologi yang dikembangkan terbukti efektif dalam menghasilkan model yang akurat, interpretable, dan robust.

### 5.2.1 Kontribusi Metodologis

Kontribusi metodologis pertama adalah implementasi Bias Correction Framework melalui `BiasCorrectTitleClassifier` yang menganalisis dan mengoreksi potensi bias dalam data sebelum proses machine learning. Framework ini menghasilkan target variable `corrected_fatigue_risk` yang lebih robust dan mengurangi risiko data leakage, sehingga model dapat belajar dari pola yang benar-benar mencerminkan kondisi fatigue mahasiswa.

```python
# Bias correction implementation
bias_corrector = BiasCorrectTitleClassifier()
corrected_data = bias_corrector.correct_bias(
    input_data=raw_dataset,
    target_column='fatigue_risk',
    correction_method='title_based'
)
```

Kontribusi kedua adalah penggunaan SHAP (SHapley Additive exPlanations) untuk feature selection yang theoretically grounded berdasarkan game theory. Metode ini memberikan interpretabilitas yang superior dibandingkan metode tradisional dan berhasil mengidentifikasi fitur-fitur linguistik sebagai prediktor terkuat untuk risiko fatigue.

Kontribusi ketiga adalah pengembangan Dual Evaluation Strategy yang mengombinasikan train-test split untuk SHAP analysis dan k-fold cross-validation untuk robustness evaluation. Strategi ini memberikan perspektif yang komprehensif tentang performa model dan berhasil mengidentifikasi overfitting secara efektif.

Kontribusi keempat adalah metodologi integrasi data multi-modal yang menggabungkan data aktivitas fisik dan produktivitas akademik melalui weekly aggregation dengan feature engineering yang menghasilkan 20 fitur informatif, termasuk fitur linguistik yang terbukti paling penting untuk prediksi fatigue.

### 5.2.2 Keunggulan Metodologi

Keunggulan pertama dari metodologi yang dikembangkan adalah interpretabilitas yang tinggi melalui SHAP analysis. Analisis ini berhasil mengungkap bahwa fitur-fitur linguistik seperti pomokit_unique_words, total_title_diversity, dan title_balance_ratio adalah prediktor terkuat, memberikan insights yang actionable untuk monitoring fatigue mahasiswa.

Keunggulan kedua adalah robustness terhadap overfitting melalui dual evaluation strategy. Pendekatan ini berhasil mengidentifikasi bahwa Logistic Regression memiliki stabilitas terbaik dengan overfitting score 9.23, dibandingkan model tree-based yang menunjukkan scores lebih dari 21, sehingga memberikan rekomendasi model yang tepat untuk dataset berukuran sedang.

Keunggulan ketiga adalah kemampuan bias mitigation melalui bias correction framework yang mengurangi potensi bias dalam klasifikasi dan menghasilkan dataset yang lebih reliable untuk training model. Hal ini memastikan bahwa prediksi yang dihasilkan mencerminkan kondisi sebenarnya dari risiko fatigue mahasiswa.

Keunggulan keempat adalah scalability metodologi yang dapat diadaptasi untuk dataset yang lebih besar dan domain aplikasi yang berbeda dengan modifikasi minimal, sehingga memberikan value yang berkelanjutan untuk penelitian serupa di masa depan.

### 5.2.3 Validasi Metodologi

Validasi metodologi dilakukan melalui berbagai pendekatan yang komprehensif. Perbandingan SHAP features dengan random features menunjukkan bahwa SHAP features memberikan keuntungan yang konsisten dengan peningkatan performa antara 0.17% hingga 1.91% dibandingkan random selection, memvalidasi efektivitas pendekatan SHAP dalam mengidentifikasi fitur yang benar-benar informatif.

Hasil cross-validation menunjukkan estimasi yang lebih conservative dan realistic dibandingkan single train-test split, memvalidasi pentingnya dual evaluation strategy dalam memberikan perspektif yang seimbang tentang performa model. Top 5 fitur menunjukkan konsistensi 100% across semua algoritma, mengkonfirmasi robustness dari feature selection yang dilakukan.

Metodologi juga berhasil mengidentifikasi model yang terlalu kompleks untuk dataset size melalui overfitting detection, memberikan guidance yang jelas untuk pemilihan model yang sesuai dengan karakteristik data yang tersedia.

## 5.3 Kesimpulan Eksperimen

Eksperimen yang dilakukan menghasilkan temuan-temuan penting tentang prediksi risiko fatigue mahasiswa dan memberikan rekomendasi praktis untuk implementasi sistem monitoring fatigue yang efektif.

### 5.3.1 Temuan Utama Eksperimen

Temuan utama pertama adalah dominasi fitur linguistik dalam prediksi risiko fatigue. Hasil SHAP analysis mengungkap bahwa fitur-fitur yang berkaitan dengan cara mahasiswa mendeskripsikan aktivitas mereka adalah prediktor terkuat untuk risiko fatigue. Pomokit_unique_words dengan importance 5.54% mencerminkan diversitas linguistik dalam deskripsi aktivitas produktivitas, total_title_diversity dengan importance 5.33% menunjukkan variasi dalam cara mengkategorisasi aktivitas, dan title_balance_ratio dengan importance 5.19% mencerminkan keseimbangan deskripsi antara aktivitas produktivitas dan fisik.

Temuan kedua berkaitan dengan performa model yang realistis melalui evaluasi komprehensif. XGBoost menunjukkan performa terbaik dengan test accuracy 79.66% namun memiliki risiko overfitting yang tinggi, sementara Logistic Regression menunjukkan stabilitas terbaik dengan performa yang konsisten antara training dan validation, menjadikannya pilihan yang lebih reliable untuk implementasi praktis.

Temuan ketiga adalah identifikasi overfitting pada dataset berukuran sedang. Model tree-based menunjukkan overfitting yang signifikan pada dataset 291 samples dengan train-validation gap lebih dari 27%, mengindikasikan bahwa kompleksitas model harus disesuaikan dengan ukuran dataset untuk mencapai generalisasi yang baik.

Temuan keempat adalah validasi efektivitas SHAP feature selection yang terbukti superior dibandingkan random selection. SHAP-based feature selection menunjukkan improvement yang konsisten untuk sebagian besar algoritma, dengan peningkatan performa antara 0.17% hingga 1.91%, kecuali XGBoost yang menunjukkan penurunan 1.48% yang mengindikasikan kompleksitas berlebihan.

### 5.3.2 Implikasi Praktis

Implikasi praktis pertama berkaitan dengan pengembangan sistem monitoring fatigue. Temuan bahwa fitur linguistik adalah prediktor terkuat mengindikasikan bahwa sistem monitoring fatigue dapat fokus pada analisis pola linguistik dalam self-reporting aktivitas sebagai early warning system, memberikan pendekatan yang lebih proaktif dalam deteksi risiko fatigue mahasiswa.

Implikasi kedua berkaitan dengan model selection untuk production deployment. Untuk mencapai akurasi maksimal, XGBoost dengan test accuracy 79.66% dapat digunakan dengan monitoring overfitting yang ketat. Untuk stabilitas jangka panjang, Logistic Regression memberikan konsistensi terbaik dengan risiko overfitting rendah. Untuk interpretabilitas, SHAP analysis memberikan explanations yang actionable bagi stakeholder dalam memahami faktor-faktor yang berkontribusi terhadap prediksi.

Implikasi ketiga adalah insights untuk feature engineering masa depan. Diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mencerminkan pola kognitif yang berkaitan dengan fatigue, memberikan direction untuk pengembangan fitur baru yang dapat meningkatkan akurasi prediksi.

Implikasi keempat berkaitan dengan considerations untuk dataset size. Untuk dataset berukuran sedang kurang dari 500 samples, model linear seperti Logistic Regression terbukti lebih reliable dibandingkan model kompleks seperti ensemble methods yang cenderung mengalami overfitting.

### 5.3.3 Rekomendasi untuk Penelitian Lanjutan

Rekomendasi pertama untuk penelitian lanjutan adalah implementasi teknik data augmentation. Mengingat overfitting yang terjadi pada model tree-based dengan dataset berukuran 291 samples, penelitian lanjutan dapat fokus pada teknik data augmentation untuk meningkatkan ukuran dataset sehingga model kompleks dapat bekerja dengan lebih optimal.

Rekomendasi kedua adalah pengembangan longitudinal analysis yang lebih mendalam. Analisis temporal yang lebih komprehensif diperlukan untuk memahami pola fatigue dalam jangka waktu yang lebih panjang, sehingga dapat mengidentifikasi tren dan siklus fatigue yang mungkin tidak terdeteksi dalam analisis cross-sectional.

Rekomendasi ketiga berkaitan dengan multi-modal feature engineering. Eksplorasi fitur-fitur baru yang menggabungkan aspek temporal, linguistik, dan behavioral dapat meningkatkan akurasi prediksi dengan memanfaatkan informasi yang lebih kaya dari berbagai modalitas data.

Rekomendasi keempat adalah pengembangan sistem real-time implementation. Sistem real-time yang dapat mengimplementasikan insights dari fitur linguistik untuk monitoring fatigue secara kontinyu akan memberikan value praktis yang tinggi dalam aplikasi monitoring kesehatan mahasiswa.

### 5.3.4 Kontribusi terhadap Body of Knowledge

Penelitian ini memberikan kontribusi signifikan terhadap bidang health informatics dan machine learning melalui beberapa aspek utama. Kontribusi pertama adalah novel insight berupa identifikasi fitur linguistik sebagai prediktor utama fatigue risk, yang membuka perspektif baru dalam memahami hubungan antara pola linguistik dan kondisi kesehatan mental mahasiswa.

Kontribusi kedua adalah methodological contribution melalui pengembangan framework bias correction dan dual evaluation strategy yang dapat diadaptasi untuk penelitian serupa di domain lain. Framework ini memberikan pendekatan yang sistematis untuk mengatasi bias dalam data dan evaluasi model yang komprehensif.

Kontribusi ketiga berupa practical application dengan memberikan rekomendasi konkret untuk implementasi sistem monitoring fatigue yang dapat diaplikasikan dalam konteks nyata monitoring kesehatan mahasiswa. Rekomendasi ini mencakup pemilihan model, feature engineering, dan strategi deployment yang praktis.

Kontribusi keempat adalah overfitting awareness melalui demonstrasi pentingnya model selection yang sesuai dengan dataset size, memberikan guidance yang valuable untuk peneliti dalam memilih kompleksitas model yang tepat berdasarkan karakteristik data yang tersedia.

Secara keseluruhan, penelitian ini berhasil mengembangkan metodologi yang robust untuk prediksi risiko fatigue mahasiswa dengan akurasi yang realistis dan interpretabilitas yang tinggi, memberikan foundation yang solid untuk pengembangan sistem monitoring kesehatan mahasiswa yang lebih efektif dan dapat diimplementasikan dalam praktik.
