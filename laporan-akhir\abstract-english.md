# ABSTRACT

This research develops a student fatigue risk classification system using SHAP feature selection on cardiovascular and productivity activity data. The dataset consists of 291 weekly observations with 20 features analyzed using four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, XGBoost) with bias correction framework implementation. Results show linguistic features dominate fatigue prediction, with pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%) as the strongest predictors. XGBoost achieved the best accuracy of 79.66% but showed overfitting risk, while Logistic Regression demonstrated superior stability (71.19% test accuracy, 69.35% CV accuracy). Title-only analysis proved effective with linguistic features contributing 15.06% of total feature importance. SHAP feature selection outperformed random selection with improvements of 0.17%-1.91%. Main research contributions include identification of linguistic features as dominant fatigue predictors, development of bias correction framework, and validation of title-only analysis for practical and non-intrusive fatigue monitoring.

**Keywords:** fatigue classification, SHAP feature selection, machine learning, linguistic features, title-only analysis, bias correction
