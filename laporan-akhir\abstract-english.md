# ABSTRACT

Fatigue is a serious problem that affects students' mental health and academic performance. This research aims to develop a student fatigue risk classification system using SHAP feature selection on cardiovascular and productivity activity data. Three main research questions were addressed: (1) what factors are most significant in predicting fatigue risk and how can they be validated through ablation study, (2) how effective are machine learning models in classifying fatigue risk levels, and (3) whether title-only analysis can provide accurate predictions without complete quantitative data.

The research methodology employed a machine learning approach with four algorithms: Logistic Regression, Random Forest, Gradient Boosting, and XGBoost. The dataset consisted of 291 weekly student observations with 20 features encompassing productivity, physical activity, temporal, and linguistic aspects. Implementation of a bias correction framework through `BiasCorrectTitleClassifier` generated a more robust target variable `corrected_fatigue_risk`. SHAP (SHapley Additive exPlanations) was utilized for feature selection and model interpretability, with a dual evaluation strategy combining train-test split for SHAP analysis and k-fold cross-validation for robustness evaluation.

Results demonstrate that linguistic features dominate fatigue risk prediction. SHAP ablation study identified pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%) as the strongest predictors, indicating that how students describe their activities contains richer information about psychological conditions compared to quantitative features. XGBoost achieved the best performance with test accuracy of 79.66% and F1-score of 79.54%, but showed high overfitting risk (CV accuracy 66.76%). Logistic Regression demonstrated superior stability with consistency between test accuracy (71.19%) and CV performance (69.35%) and the lowest overfitting score (9.23). Title-only analysis proved capable of providing accurate predictions with linguistic features contributing a total of 15.06% of feature importance, higher than other quantitative features.

Research contributions include identification of linguistic features as primary fatigue risk predictors, development of bias correction framework and dual evaluation strategy, and validation of title-only analysis effectiveness for practical and non-intrusive fatigue monitoring. SHAP feature selection proved superior to random selection with improvements of 0.17%-1.91% across most algorithms. This research provides a foundation for developing student health monitoring systems that focus on linguistic pattern analysis as an early warning system.

**Keywords:** fatigue classification, SHAP feature selection, machine learning, linguistic features, title-only analysis, bias correction
