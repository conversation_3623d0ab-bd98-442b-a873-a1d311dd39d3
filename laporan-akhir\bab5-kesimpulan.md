# BAB V

# KESIMPULAN

## 5.1 Kesimpulan Masalah

Penelitian ini berhasil mengidentifikasi dan mengatasi masalah utama dalam prediksi risiko fatigue mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Masalah yang dihadapi meliputi kompleksitas dalam mengintegrasikan data heterogen dari berbagai sumber, ketidakseimbangan distribusi kelas dalam dataset, dan kebutuhan akan interpretabilitas model untuk aplikasi praktis dalam monitoring kesehatan mahasiswa.

### 5.1.1 Identifikasi Masalah Utama

Masalah utama yang berhasil diidentifikasi dalam penelitian ini adalah:

**1. Kompleksitas Integrasi Data Multi-Modal:**
Dataset terdiri dari data aktivitas fisik (Strava) dan data produktivitas akademik (Pomokit) yang memiliki karakteristik temporal dan struktural yang berbeda. Integrasi kedua jenis data ini memerlukan pendekatan khusus untuk mempertahankan informasi temporal sambil menciptakan representasi yang konsisten untuk machine learning.

**2. Ketidakseimbangan Distribusi Kelas:**
Distribusi target variable menunjukkan ketidakseimbangan yang signifikan dengan high_risk hanya 7.9% (23 observasi), medium_risk 52.6% (153 observasi), dan low_risk 39.5% (115 observasi) dari total 291 samples. Ketidakseimbangan ini berpotensi menyebabkan bias dalam prediksi dan memerlukan strategi khusus dalam modeling.

**3. Bias dalam Klasifikasi Fatigue:**
Proses klasifikasi fatigue risk berpotensi mengandung bias yang berasal dari subjektivitas dalam labeling atau korelasi yang tidak diinginkan dengan fitur-fitur tertentu. Hal ini memerlukan implementasi bias correction untuk memastikan validitas prediksi.

**4. Kebutuhan Interpretabilitas Model:**
Untuk aplikasi dalam monitoring kesehatan mahasiswa, model tidak hanya harus akurat tetapi juga interpretable. Stakeholder perlu memahami faktor-faktor apa yang berkontribusi terhadap prediksi risiko fatigue untuk dapat mengambil tindakan preventif yang tepat.

### 5.1.2 Dampak Masalah Terhadap Prediksi Fatigue

Masalah-masalah yang diidentifikasi memiliki dampak signifikan terhadap akurasi dan reliabilitas prediksi fatigue:

- **Noise dalam Data**: Integrasi data yang tidak optimal dapat menghasilkan noise yang mengurangi signal-to-noise ratio
- **Overfitting Risk**: Ketidakseimbangan kelas dan dataset berukuran sedang (291 samples) meningkatkan risiko overfitting
- **Bias Amplification**: Bias dalam data dapat diamplifikasi oleh model machine learning
- **Limited Actionability**: Model yang tidak interpretable sulit diimplementasikan dalam praktik monitoring kesehatan

### 5.1.3 Validasi Masalah Melalui Eksperimen

Eksperimen yang dilakukan memvalidasi keberadaan masalah-masalah tersebut:

- **Overfitting Evidence**: Model tree-based menunjukkan overfitting score >21 dengan train-validation gap >27%
- **Feature Importance Variability**: Tanpa SHAP analysis, sulit mengidentifikasi fitur yang benar-benar informatif
- **Performance Inconsistency**: Perbedaan signifikan antara train-test split (79.66%) dan cross-validation (66.76%) mengkonfirmasi masalah generalisasi

## 5.2 Kesimpulan Metode

Penelitian ini mengembangkan metodologi komprehensif yang menggabungkan bias correction, SHAP-based feature selection, dan dual evaluation strategy untuk mengatasi masalah-masalah yang diidentifikasi. Metodologi yang dikembangkan terbukti efektif dalam menghasilkan model yang akurat, interpretable, dan robust.

### 5.2.1 Kontribusi Metodologis

**1. Bias Correction Framework:**
Implementasi `BiasCorrectTitleClassifier` yang menganalisis dan mengoreksi potensi bias dalam data sebelum proses machine learning. Framework ini menghasilkan target variable `corrected_fatigue_risk` yang lebih robust dan mengurangi risiko data leakage.

```python
# Bias correction implementation
bias_corrector = BiasCorrectTitleClassifier()
corrected_data = bias_corrector.correct_bias(
    input_data=raw_dataset,
    target_column='fatigue_risk',
    correction_method='title_based'
)
```

**2. SHAP-based Feature Selection:**
Penggunaan SHAP (SHapley Additive exPlanations) untuk feature selection yang theoretically grounded berdasarkan game theory. Metode ini memberikan interpretabilitas yang superior dibandingkan metode tradisional dan mengidentifikasi fitur-fitur linguistik sebagai prediktor terkuat.

**3. Dual Evaluation Strategy:**
Kombinasi train-test split untuk SHAP analysis dan k-fold cross-validation untuk robustness evaluation. Strategi ini memberikan perspektif yang komprehensif tentang performa model dan mengidentifikasi overfitting secara efektif.

**4. Multi-Modal Data Integration:**
Metodologi integrasi data aktivitas fisik dan produktivitas akademik melalui weekly aggregation dengan feature engineering yang menghasilkan 20 fitur informatif termasuk fitur linguistik yang terbukti paling penting.

### 5.2.2 Keunggulan Metodologi

**1. Interpretabilitas Tinggi:**
SHAP analysis mengungkap bahwa fitur-fitur linguistik (pomokit_unique_words, total_title_diversity, title_balance_ratio) adalah prediktor terkuat, memberikan insights yang actionable untuk monitoring fatigue.

**2. Robustness terhadap Overfitting:**
Dual evaluation strategy berhasil mengidentifikasi bahwa Logistic Regression memiliki stabilitas terbaik (overfitting score: 9.23) dibandingkan model tree-based (scores >21).

**3. Bias Mitigation:**
Bias correction framework mengurangi potensi bias dalam klasifikasi dan menghasilkan dataset yang lebih reliable untuk training model.

**4. Scalability:**
Metodologi dapat diadaptasi untuk dataset yang lebih besar dan domain aplikasi yang berbeda dengan modifikasi minimal.

### 5.2.3 Validasi Metodologi

Validasi metodologi dilakukan melalui:

- **SHAP vs Random Features**: SHAP features menunjukkan keuntungan +0.17% hingga +1.91% dibandingkan random selection
- **Cross-Validation Consistency**: Hasil CV menunjukkan estimasi yang lebih conservative dan realistic
- **Feature Consistency**: Top 5 fitur menunjukkan konsistensi 100% across semua algoritma
- **Overfitting Detection**: Berhasil mengidentifikasi model yang terlalu kompleks untuk dataset size

## 5.3 Kesimpulan Eksperimen

Eksperimen yang dilakukan menghasilkan temuan-temuan penting tentang prediksi risiko fatigue mahasiswa dan memberikan rekomendasi praktis untuk implementasi sistem monitoring fatigue yang efektif.

### 5.3.1 Temuan Utama Eksperimen

**1. Dominasi Fitur Linguistik:**
Hasil SHAP analysis mengungkap bahwa fitur-fitur yang berkaitan dengan cara mahasiswa mendeskripsikan aktivitas mereka adalah prediktor terkuat untuk risiko fatigue:

- **pomokit_unique_words** (5.54% importance): Diversitas linguistik dalam deskripsi aktivitas produktivitas
- **total_title_diversity** (5.33% importance): Variasi dalam cara mengkategorisasi aktivitas
- **title_balance_ratio** (5.19% importance): Keseimbangan deskripsi antara aktivitas produktivitas dan fisik

**2. Performa Model yang Realistis:**
Evaluasi komprehensif menunjukkan performa yang realistis dengan XGBoost sebagai model terbaik (79.66% test accuracy) namun dengan risiko overfitting yang tinggi. Logistic Regression menunjukkan stabilitas terbaik dengan performa yang konsisten antara training dan validation.

**3. Overfitting pada Dataset Berukuran Sedang:**
Model tree-based menunjukkan overfitting yang signifikan pada dataset 291 samples, dengan train-validation gap >27%. Hal ini mengindikasikan bahwa kompleksitas model harus disesuaikan dengan ukuran dataset.

**4. Efektivitas SHAP Feature Selection:**
SHAP-based feature selection terbukti superior dibandingkan random selection, dengan improvement yang konsisten untuk sebagian besar algoritma (kecuali XGBoost yang menunjukkan -1.48%).

### 5.3.2 Implikasi Praktis

**1. Sistem Monitoring Fatigue:**
Temuan bahwa fitur linguistik adalah prediktor terkuat mengindikasikan bahwa sistem monitoring fatigue dapat fokus pada analisis pola linguistik dalam self-reporting aktivitas sebagai early warning system.

**2. Model Selection untuk Production:**
- **Untuk Akurasi Maksimal**: XGBoost (79.66% accuracy) dengan monitoring overfitting
- **Untuk Stabilitas**: Logistic Regression dengan konsistensi terbaik dan risiko overfitting rendah
- **Untuk Interpretabilitas**: SHAP analysis memberikan explanations yang actionable

**3. Feature Engineering Insights:**
Diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mencerminkan pola kognitif yang berkaitan dengan fatigue, memberikan direction untuk pengembangan fitur baru.

**4. Dataset Size Considerations:**
Untuk dataset berukuran sedang (<500 samples), model linear seperti Logistic Regression lebih reliable dibandingkan model kompleks seperti ensemble methods.

### 5.3.3 Rekomendasi untuk Penelitian Lanjutan

**1. Data Augmentation:**
Mengingat overfitting pada model tree-based, penelitian lanjutan dapat fokus pada teknik data augmentation untuk meningkatkan ukuran dataset.

**2. Longitudinal Analysis:**
Analisis temporal yang lebih mendalam untuk memahami pola fatigue dalam jangka waktu yang lebih panjang.

**3. Multi-Modal Feature Engineering:**
Eksplorasi fitur-fitur baru yang menggabungkan aspek temporal, linguistik, dan behavioral untuk meningkatkan akurasi prediksi.

**4. Real-Time Implementation:**
Pengembangan sistem real-time yang dapat mengimplementasikan insights dari fitur linguistik untuk monitoring fatigue secara kontinyu.

### 5.3.4 Kontribusi terhadap Body of Knowledge

Penelitian ini memberikan kontribusi signifikan terhadap bidang health informatics dan machine learning:

- **Novel Insight**: Identifikasi fitur linguistik sebagai prediktor utama fatigue risk
- **Methodological Contribution**: Framework bias correction dan dual evaluation strategy
- **Practical Application**: Rekomendasi konkret untuk implementasi sistem monitoring fatigue
- **Overfitting Awareness**: Demonstrasi pentingnya model selection yang sesuai dengan dataset size

Secara keseluruhan, penelitian ini berhasil mengembangkan metodologi yang robust untuk prediksi risiko fatigue mahasiswa dengan akurasi yang realistis dan interpretabilitas yang tinggi, memberikan foundation yang solid untuk pengembangan sistem monitoring kesehatan mahasiswa yang lebih efektif.
