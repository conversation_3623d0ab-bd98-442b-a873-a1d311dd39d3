# BAB I

# PENDAHULUAN

## 1.1 Latar Belakang

Dalam era digital yang semakin berkembang pesat, mahas<PERSON><PERSON> menghadapi tantangan yang kompleks dalam menyeimbangkan aktivitas fisik dan produktivitas akademik. <PERSON>a hidup sedentari yang dominan di kalangan mahas<PERSON>wa, teru<PERSON>a yang terlibat dalam bidang teknologi informasi, telah menjadi perhatian serius dalam konteks kesehatan kardiovaskular dan manajemen fatigue.

Aktivitas kardiovaskular memiliki peran penting dalam menjaga kesehatan fisik dan mental mahasiswa. Pen<PERSON>tian menunjukkan bahwa aktivitas fisik yang teratur dapat meningkatkan fungsi kognitif, mengu<PERSON><PERSON> tingkat stres, dan meningkatkan produktivitas akademik [1], [2]. <PERSON><PERSON>, banyak mahasiswa mengalami kesulitan dalam mempertahankan rutinitas aktivitas fisik yang konsisten karena tuntutan akademik yang tinggi dan manajemen waktu yang kurang efektif [3].

Fatigue atau kelelahan merupakan fenomena multidimensional yang dapat mempengaruhi performa akademik dan kualitas hidup mahasiswa [11]. Fatigue tidak hanya berkaitan dengan kelelahan fisik, tetapi juga meliputi aspek mental dan emosional yang dapat berdampak pada motivasi, konsentrasi, dan kemampuan pengambilan keputusan [4]. Identifikasi dini terhadap risiko fatigue menjadi krusial untuk mencegah dampak negatif yang lebih luas [5].

Perkembangan teknologi wearable dan aplikasi mobile telah membuka peluang baru dalam monitoring aktivitas fisik dan produktivitas secara real-time. Platform seperti Strava untuk tracking aktivitas kardiovaskular dan Pomokit untuk manajemen produktivitas menyediakan data yang kaya dan dapat dianalisis untuk memahami pola perilaku mahasiswa. Integrasi data dari kedua platform ini memungkinkan analisis holistik terhadap hubungan antara aktivitas fisik dan produktivitas akademik.

Machine learning dan artificial intelligence telah menunjukkan potensi besar dalam analisis data kesehatan dan prediksi risiko [20]. Penerapan algoritma klasifikasi untuk prediksi fatigue berdasarkan data aktivitas fisik dan produktivitas dapat memberikan insight yang valuable untuk intervensi preventif [18], [29]. Pendekatan ini memungkinkan personalisasi strategi manajemen kesehatan berdasarkan pola individual setiap mahasiswa [19].

Penelitian ini menjadi relevan karena kurangnya studi yang mengintegrasikan data aktivitas kardiovaskular dan produktivitas akademik untuk prediksi fatigue pada populasi mahasiswa Indonesia. Mayoritas penelitian sebelumnya fokus pada satu aspek saja atau menggunakan data sekunder, sehingga tidak dapat menangkap kompleksitas hubungan antara aktivitas fisik, produktivitas, dan fatigue dalam konteks kehidupan mahasiswa yang sesungguhnya.

## 1.2 Rumusan Masalah

Berdasarkan latar belakang yang telah diuraikan, penelitian ini merumuskan beberapa permasalahan utama:

1.	Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat dival-idasi melalui ablation study 
2.	Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas 
3.	Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap

## 1.3 Tujuan Penelitian

### 1.3.1 Tujuan Umum

Mengembangkan sistem prediksi risiko fatigue pada mahasiswa melalui analisis terintegrasi data aktivitas kardiovaskular dan produktivitas akademik menggunakan pendekatan machine learning.

### 1.3.2 Tujuan Khusus

1. **Menganalisis korelasi antara aktivitas kardiovaskular dan produktivitas akademik mahasiswa**

    - Mengidentifikasi pola hubungan antara metrik aktivitas fisik dan indikator produktivitas
    - Mengevaluasi signifikansi statistik dari korelasi yang ditemukan
    - Menganalisis variabilitas temporal dalam hubungan aktivitas fisik-produktivitas

2. **Mengidentifikasi faktor-faktor prediktor utama risiko fatigue melalui ablation study**

    - Melakukan systematic ablation study untuk menentukan kontribusi setiap fitur
    - Menganalisis feature importance dan dampak removal fitur terhadap performa model
    - Mengevaluasi robustness model terhadap variasi set fitur yang digunakan
    - Mengembangkan ranking fitur berdasarkan kontribusi prediktif

3. **Mengembangkan dan mengevaluasi model machine learning untuk klasifikasi fatigue**

    - Mengimplementasikan berbagai algoritma klasifikasi (Random Forest, SVM, Neural Networks)
    - Melakukan hyperparameter tuning untuk optimasi performa model
    - Mengevaluasi model menggunakan metrik akurasi, precision, recall, dan F1-score
    - Mengimplementasikan teknik pencegahan data leakage dan bias correction

4. **Melakukan ablation study komprehensif untuk validasi metodologi**

    - Mengevaluasi dampak individual dan kombinatorial dari setiap grup fitur
    - Menganalisis degradasi performa model ketika fitur-fitur kunci dihilangkan
    - Mengidentifikasi minimal feature set yang masih memberikan performa optimal
    - Memvalidasi stabilitas dan generalisasi model melalui systematic feature removal

5. **Mengeksplorasi efektivitas title-only analysis untuk prediksi fatigue**

    - Mengembangkan pipeline text-based feature extraction untuk analisis judul aktivitas
    - Membandingkan akurasi prediksi title-only dengan model berbasis data kuantitatif
    - Mengidentifikasi kata kunci dan pola linguistik yang berkaitan dengan fatigue
    - Mengevaluasi robustness title-based prediction melalui ablation study

6. **Menganalisis dampak gamifikasi terhadap konsistensi aktivitas dan fatigue**

    - Mengevaluasi hubungan antara achievement rate dan tingkat fatigue
    - Menganalisis efektivitas elemen gamifikasi dalam mempertahankan motivasi
    - Mengidentifikasi threshold optimal untuk balance gamifikasi
    - Melakukan ablation study pada fitur gamifikasi untuk menentukan elemen paling berpengaruh

7. **Menghasilkan rekomendasi praktis untuk manajemen fatigue mahasiswa**
    - Mengembangkan guidelines berbasis evidence untuk pencegahan fatigue
    - Menyusun strategi personalisasi berdasarkan profil risiko individual
    - Merancang framework monitoring dan early warning system
    - Menyediakan interpretable model results berdasarkan temuan ablation study

## 1.4 Manfaat Penelitian

### 1.4.1 Manfaat Teoritis

1. **Kontribusi terhadap Body of Knowledge** - Memperkaya literatur tentang hubungan aktivitas kardiovaskular dan fatigue pada populasi mahasiswa serta memberikan insight baru tentang integrasi data multi-platform untuk health analytics dan pengembangan framework teoritis untuk prediksi fatigue berbasis behavioral data.

2. **Pengembangan Metodologi Penelitian** - Memperkenalkan pendekatan novel dalam analisis data aktivitas fisik dan produktivitas, mengembangkan protokol penelitian untuk studi longitudinal berbasis wearable technology, dan menyediakan baseline methodology untuk penelitian serupa di masa depan.

3. **Advancement dalam Machine Learning untuk Healthcare** - Mengeksplorasi aplikasi algoritma klasifikasi untuk prediksi fatigue, mengembangkan teknik feature engineering khusus untuk data aktivitas mahasiswa, memberikan kontribusi pada pengembangan AI-driven health monitoring systems, dan menyediakan metodologi ablation study yang rigorous untuk validasi model healthcare.

4. **Kontribusi Metodologis dalam Feature Analysis** - Mengembangkan framework systematic ablation study untuk health prediction models, menyediakan protokol evaluasi robustness model melalui feature removal analysis, dan memberikan insight tentang interpretability dan explainability dalam healthcare AI.

### 1.4.2 Manfaat Praktis

1. **Untuk Mahasiswa** - Menyediakan tools untuk self-assessment risiko fatigue, memberikan rekomendasi personal untuk optimasi aktivitas fisik dan produktivitas, serta meningkatkan awareness tentang pentingnya balance antara aktivitas fisik dan akademik.

2. **Untuk Institusi Pendidikan** - Menyediakan data empiris untuk pengembangan program wellness mahasiswa, memberikan insight untuk desain kurikulum yang mempertimbangkan aspek kesehatan, dan mendukung implementasi early warning system untuk student well-being.

3. **Untuk Pengembang Aplikasi Kesehatan** - Menyediakan evidence-based features untuk aplikasi fitness dan productivity, memberikan guidelines untuk integrasi data multi-platform, dan mendukung pengembangan algoritma prediksi yang lebih akurat.

4. **Untuk Peneliti dan Praktisi Kesehatan** - Menyediakan dataset dan methodology yang dapat direplikasi, memberikan insight untuk pengembangan intervensi preventif fatigue, dan mendukung penelitian lanjutan dalam bidang digital health.

## 1.5 Ruang Lingkup Penelitian

1. **Ruang Lingkup Metodologis** - Penelitian menggunakan desain cross-sectional dengan elemen longitudinal terbatas untuk menganalisis data aktivitas mahasiswa melalui observational study dengan penerapan algoritma machine learning untuk klasifikasi risiko fatigue, dimana data dikumpulkan melalui platform digital Strava untuk aktivitas kardiovaskular dan Pomokit untuk produktivitas akademik dengan potensi bias dalam self-reported data dan keterbatasan dalam capturing aktivitas non-digital.

2. **Ruang Lingkup Teknis** - Analisis terbatas pada data dari dua platform utama yaitu Strava untuk aktivitas kardiovaskular dan Pomokit untuk produktivitas dengan implementasi algoritma klasifikasi supervised learning seperti Random Forest, SVM, dan Neural Networks, mencakup feature engineering, hyperparameter tuning, systematic ablation study, dan evaluasi menggunakan metrik standar klasifikasi dengan ketergantungan pada akurasi sensor dan algoritma platform yang digunakan.

3. **Ruang Lingkup Populasi dan Sampel** - Penelitian fokus pada mahasiswa Indonesia dengan akses teknologi digital yang aktif menggunakan aplikasi fitness dan productivity tracking, dengan karakteristik memiliki literasi teknologi yang memadai dan motivasi untuk self-monitoring, dimana hasil penelitian spesifik untuk konteks mahasiswa Indonesia dengan generalisasi terbatas pada populasi dengan karakteristik demografis serupa dan representativitas terbatas pada mahasiswa dengan socioeconomic status tertentu.

4. **Ruang Lingkup Temporal** - Penelitian mencakup periode pengumpulan data dalam timeframe terbatas yang memungkinkan analisis pola aktivitas mingguan mahasiswa pada data historis untuk mengidentifikasi pattern yang berkaitan dengan risiko fatigue, dimana variasi seasonal dan cyclical tidak sepenuhnya tercapture dan tidak melakukan long-term follow-up namun fokus pada pengembangan model prediktif yang dapat diaplikasikan untuk monitoring real-time.

5. **Ruang Lingkup Analisis Data** - Evaluasi model menggunakan metrik standar klasifikasi seperti akurasi, precision, recall, dan F1-score dengan penerapan teknik pencegahan data leakage dan bias correction, serta eksplorasi title-only analysis sebagai pendekatan alternatif menggunakan text-based feature extraction pada deskripsi aktivitas untuk prediksi fatigue dengan validasi model terbatas pada dataset yang sama (internal validation).

## 1.6 Sistematika Penulisan

Laporan penelitian ini disusun dalam sistematika sebagai berikut:

**BAB I PENDAHULUAN**
Bab ini menguraikan latar belakang penelitian, rumusan masalah, tujuan penelitian, manfaat penelitian, ruang lingkup penelitian, dan sistematika penulisan. Bab ini memberikan gambaran komprehensif tentang konteks, motivasi, dan scope penelitian yang dilakukan.

**BAB II TINJAUAN PUSTAKA**
Bab ini menyajikan review literatur yang relevan dengan topik penelitian, meliputi teori tentang aktivitas kardiovaskular, fatigue, produktivitas akademik, machine learning untuk healthcare, dan penelitian terdahulu yang berkaitan. Bab ini juga menguraikan kerangka teoritis dan hipotesis penelitian.

**BAB III METODOLOGI PENELITIAN**
Bab ini menjelaskan desain penelitian, populasi dan sampel, teknik pengumpulan data, instrumen penelitian, preprocessing data, algoritma machine learning yang digunakan, dan metode evaluasi. Bab ini memberikan detail teknis yang memungkinkan replikasi penelitian.

**BAB IV HASIL DAN PEMBAHASAN**
Bab ini menyajikan hasil analisis data, meliputi statistik deskriptif, analisis korelasi, hasil klasifikasi fatigue, evaluasi model machine learning, dan interpretasi findings. Pembahasan mengaitkan hasil dengan teori dan penelitian sebelumnya.

**BAB V KESIMPULAN DAN SARAN**
Bab ini merangkum kesimpulan utama penelitian, implikasi teoritis dan praktis, ruang lingkup dan keterbatasan penelitian, dan saran untuk penelitian lanjutan. Bab ini juga menyajikan rekomendasi praktis berdasarkan temuan penelitian.

Setiap bab disusun secara sistematis dan logis untuk memberikan pemahaman yang komprehensif tentang penelitian yang dilakukan, mulai dari konseptualisasi hingga implementasi dan evaluasi hasil.
