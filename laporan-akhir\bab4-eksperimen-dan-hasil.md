# BAB IV

# EKSPERIMEN DAN HASIL

## 4.1 Eksperimen

Eksperimen dalam penelitian ini dirancang untuk mengevaluasi efektivitas model machine learning dalam memprediksi risiko fatigue pada mahasiswa berdasarkan data aktivitas kardiovaskular dan produktivitas akademik. Bagian ini menjelaskan setup eksperimen, implementasi model, dan proses evaluasi yang dilakukan.

### 4.1.1 Setup Eksperimen

Setup eksperimen diimplementasikan menggunakan Python 3.12 dengan library scikit-learn 1.3.0, XGBoost 2.0.0, dan SHAP 0.43.0 untuk analisis feature importance. Seluruh eksperimen dijalankan pada komputer dengan spesifikasi standar untuk memastikan reproducibility dan aksesibilitas implementasi. Untuk memastikan reproducibility, semua random seed diatur ke nilai 42.

Dataset yang digunakan dalam eksperimen terdiri dari 291 observasi mingguan yang telah melalui proses bias correction, dengan distribusi kelas: 115 observasi (39.5%) low_risk, 153 observasi (52.6%) medium_risk, dan 23 observasi (7.9%) high_risk. Dataset menggunakan target variable `corrected_fatigue_risk` yang telah melalui proses bias correction untuk mengatasi potensi bias dalam klasifikasi fatigue. Dataset dibagi menjadi training set (80%) dan testing set (20%) menggunakan stratified sampling untuk mempertahankan distribusi kelas.

```python
# Setup dataset split dengan bias-corrected target
X = dataset.drop(columns=['corrected_fatigue_risk'])
y = dataset['corrected_fatigue_risk']

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"Training set: {X_train.shape[0]} samples")  # 232 samples
print(f"Testing set: {X_test.shape[0]} samples")    # 59 samples
print(f"Features: {X.shape[1]}")                    # 20 features
```

**Dataset Features (20 fitur setelah bias correction):**

-   **Productivity features**: avg_cycles, total_cycles, productivity_points, work_days, weekly_efficiency
-   **Activity features**: activity_days, avg_distance_km, total_distance_km, activity_points
-   **Title diversity features**: pomokit_title_count, strava_title_count, pomokit_unique_words, strava_unique_words, total_title_diversity, title_balance_ratio
-   **Time features**: avg_time_minutes, total_time_minutes
-   **Gamification features**: achievement_rate, gamification_balance
-   **Consistency features**: consistency_score

### 4.1.2 Implementasi Model

Empat algoritma machine learning diimplementasikan dan dibandingkan dalam eksperimen ini: Logistic Regression (LR), Random Forest (RF), Gradient Boosting (GB), dan XGBoost (XGB). Setiap model dikonfigurasi dengan parameter default yang telah terbukti efektif untuk klasifikasi multi-class dan dievaluasi menggunakan SHAP ablation study untuk analisis feature importance yang komprehensif.

![Algorithm Comparison](results/all_algorithms_comparison/all_four_algorithms_comparison.png)

_Gambar 4.1: Perbandingan Implementasi Keempat Algoritma Machine Learning_

**Justifikasi Pemilihan Algoritma:**

1. **Logistic Regression**: Baseline model linear yang interpretable, cepat untuk training, dan memberikan probabilitas yang well-calibrated untuk klasifikasi fatigue risk
2. **Random Forest**: Ensemble method yang robust terhadap overfitting, dapat menangani feature interactions, dan memberikan feature importance yang stabil
3. **Gradient Boosting**: Sequential ensemble yang optimal untuk tabular data dengan performa tinggi dan kemampuan menangani complex patterns
4. **XGBoost**: State-of-the-art gradient boosting dengan optimasi advanced, regularization, dan performa superior pada dataset tabular

**Model Configuration Strategy:**

Strategi konfigurasi model dirancang untuk mengatasi tantangan spesifik dalam dataset bias-corrected fatigue risk dan memastikan performa optimal. Mengingat distribusi kelas yang tidak seimbang (high_risk hanya 7.9%), class balancing diterapkan secara otomatis oleh algoritma atau melalui stratified sampling. Reproducibility dijamin dengan menetapkan `random_state=42` untuk semua model, memastikan hasil yang konsisten dan dapat direplikasi. Model dikonfigurasi dengan parameter default yang telah terbukti efektif untuk menghindari overfitting pada dataset berukuran sedang (291 samples).

```python
# Model implementations dengan konfigurasi optimal
models = {
    'logistic_regression': LogisticRegression(
        random_state=42,
        max_iter=1000,
        solver='liblinear'  # Optimal untuk dataset kecil-sedang
    ),
    'random_forest': RandomForestClassifier(
        n_estimators=100,
        random_state=42,
        max_features='sqrt',  # Mengurangi overfitting
        min_samples_split=2
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=100,
        random_state=42,
        learning_rate=0.1,
        max_depth=3  # Shallow trees untuk menghindari overfitting
    ),
    'xgboost': XGBClassifier(
        n_estimators=100,
        random_state=42,
        learning_rate=0.1,
        max_depth=6,
        eval_metric='mlogloss',
        objective='multi:softprob'  # Multi-class classification
    )
}
```

Hyperparameter tuning dilakukan untuk setiap model menggunakan GridSearchCV dengan 5-fold cross-validation. Parameter grid untuk Random Forest dan Gradient Boosting ditunjukkan di bawah ini:

```python
# Hyperparameter tuning
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 150, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 150, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}

for model_name in ['random_forest', 'gradient_boosting']:
    grid_search = GridSearchCV(
        models[model_name], param_grids[model_name],
        cv=5, scoring='f1_macro', n_jobs=-1
    )
    grid_search.fit(X_train, y_train)
    models[model_name] = grid_search.best_estimator_
    print(f"Best parameters for {model_name}: {grid_search.best_params_}")
```

### 4.1.3 SHAP-based Feature Selection

Eksperimen feature selection dilakukan menggunakan SHAP (SHapley Additive exPlanations) untuk mengidentifikasi fitur-fitur yang paling berkontribusi terhadap prediksi risiko fatigue. SHAP dipilih sebagai metode utama karena kemampuannya memberikan interpretabilitas yang komprehensif dan theoretically grounded berdasarkan game theory.

SHAP dipilih sebagai metode feature selection karena memiliki beberapa keunggulan signifikan dibandingkan metode tradisional. Metode ini menyediakan individual prediction explanations dengan interpretabilitas tinggi, memungkinkan pemahaman mendalam tentang kontribusi setiap fitur terhadap prediksi spesifik. SHAP mampu melakukan feature interaction detection untuk memahami hubungan kompleks antar fitur yang mungkin tidak terdeteksi oleh metode lain. Keunggulan lainnya adalah kemampuan global dan local interpretability yang komprehensif, memberikan insights baik pada level dataset maupun prediksi individual. Secara teoritis, SHAP memiliki fondasi yang kuat berdasarkan game theory melalui Shapley values, memberikan justifikasi matematis yang solid. Metode ini juga bersifat model-agnostic, dapat diterapkan pada semua algoritma machine learning, dan menyediakan positive/negative contribution analysis yang detail untuk setiap fitur.

**Implementasi SHAP Explainers:**

Untuk memastikan robustness dan konsistensi hasil across different model types, implementasi menggunakan SHAP KernelExplainer yang bersifat model-agnostic dan dapat menangani semua jenis algoritma dengan pendekatan yang unified.

```python
# SHAP KernelExplainer implementation untuk semua model
def create_shap_explainer(model, X_background):
    """Create SHAP KernelExplainer for any model type"""

    # Sample background data untuk efisiensi komputasi
    background_data = X_background.iloc[:50]

    def model_predict(X):
        """Wrapper function untuk model prediction"""
        if hasattr(model, 'predict_proba'):
            return model.predict_proba(X)
        else:
            return model.predict(X)

    explainer = shap.KernelExplainer(model_predict, background_data)
    return explainer

# Create explainers untuk semua model
explainers = {}
for model_name, model in models.items():
    explainers[model_name] = create_shap_explainer(model, X_train)
```

SHAP analysis dilakukan pada semua model yang telah dilatih untuk mengidentifikasi kontribusi setiap fitur terhadap prediksi secara konsisten across algorithms:

```python
# SHAP analysis untuk semua model
shap_results = {}

for model_name, model in models.items():
    model.fit(X_train, y_train)

    # Pilih explainer berdasarkan jenis model
    if model_name == 'logistic_regression':
        explainer = shap.LinearExplainer(model, X_train)
    else:  # Tree-based models
        explainer = shap.TreeExplainer(model)

    shap_values = explainer.shap_values(X_train)

    # Calculate global feature importance
    if isinstance(shap_values, list):  # Multi-class
        shap_importance = np.abs(np.array(shap_values)).mean(axis=(0, 1))
    else:  # Binary or single output
        shap_importance = np.abs(shap_values).mean(axis=0)

    feature_importance = dict(zip(X_train.columns, shap_importance))
    shap_results[model_name] = feature_importance

    print(f"Top 5 features for {model_name}:")
    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
    for feature, importance in sorted_features[:5]:
        print(f"  {feature}: {importance:.4f}")
```

**SHAP Consensus Analysis:**

Untuk mengidentifikasi fitur yang paling konsisten across semua algoritma, dilakukan consensus analysis berdasarkan ranking SHAP importance:

```python
# Consensus analysis dari semua model
feature_consensus = {}
for model_name, importance_dict in shap_results.items():
    sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # Berikan score berdasarkan ranking (top feature = score tertinggi)
    for rank, (feature, importance) in enumerate(sorted_features):
        if feature not in feature_consensus:
            feature_consensus[feature] = []

        # Score: jumlah fitur - ranking (semakin tinggi ranking, semakin tinggi score)
        score = len(sorted_features) - rank
        feature_consensus[feature].append(score)

# Hitung rata-rata consensus score
consensus_scores = {}
for feature, scores in feature_consensus.items():
    consensus_scores[feature] = np.mean(scores)

# Sort berdasarkan consensus score
final_ranking = sorted(consensus_scores.items(), key=lambda x: x[1], reverse=True)

print("Top 10 features by SHAP consensus:")
for feature, score in final_ranking[:10]:
    print(f"{feature}: {score:.2f}")
```

Proses SHAP-based feature selection divisualisasikan untuk menunjukkan ranking dan konsistensi fitur across different algorithms.

![SHAP Feature Consistency](results/visualizations/feature_consistency_20250721_211145.png)

_Gambar 4.1: SHAP Feature Consistency Analysis Across All Algorithms_

Visualisasi menunjukkan bagaimana setiap algoritma memberikan ranking yang konsisten untuk fitur-fitur teratas, dengan `productivity_points`, `strava_title_count`, dan `gamification_balance` secara konsisten muncul di posisi teratas across semua model.

**SHAP vs Random Feature Validation:**

Untuk memvalidasi efektivitas SHAP-based feature selection, dilakukan perbandingan dengan random feature selection sebagai baseline control.

![SHAP vs Random Features](../results/feature_validation/shap_vs_random.png)

_Gambar 4.2: Perbandingan Performa SHAP Features vs Random Features_

Gambar 4.2 menunjukkan perbandingan performa antara fitur yang dipilih berdasarkan SHAP importance dengan fitur yang dipilih secara random. Grafik menampilkan akurasi model untuk berbagai jumlah fitur (3, 5, 10, 15) menggunakan keempat algoritma. SHAP-selected features secara konsisten menunjukkan performa yang superior dibandingkan random features across semua algoritma dan jumlah fitur. Perbedaan performa paling signifikan terlihat pada Random Forest dan Gradient Boosting, di mana SHAP features mencapai akurasi >90% bahkan dengan hanya 3 fitur, sementara random features dengan jumlah yang sama hanya mencapai ~70-80%. Hasil ini memvalidasi bahwa SHAP berhasil mengidentifikasi fitur-fitur yang benar-benar informatif untuk prediksi risiko fatigue.

### 4.1.4 SHAP-based Minimal Feature Set Experiment

Berdasarkan hasil SHAP consensus analysis, eksperimen tambahan dilakukan untuk mengevaluasi performa model dengan hanya menggunakan 3 fitur teratas dari ranking SHAP: `pomokit_title_count`, `gamification_balance`, dan `total_distance_km`. Tujuan eksperimen ini adalah untuk menentukan apakah subset fitur minimal berdasarkan SHAP importance dapat memberikan performa yang sebanding dengan menggunakan seluruh fitur.

```python
# SHAP-based minimal feature set experiment
# Gunakan top 3 features dari SHAP consensus (excluding consistency_score dan total_cycles)
top_shap_features = ['pomokit_title_count', 'gamification_balance', 'total_distance_km']
X_train_minimal = X_train[top_shap_features]
X_test_minimal = X_test[top_shap_features]

minimal_results = {}
for model_name, model in models.items():
    # Train model dengan minimal features
    model.fit(X_train_minimal, y_train)
    y_pred = model.predict(X_test_minimal)

    # Evaluate performance
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred, average='macro')

    # Cross-validation untuk validasi
    cv_scores = cross_val_score(model, X_train_minimal, y_train, cv=5, scoring='f1_macro')

    minimal_results[model_name] = {
        'accuracy': accuracy,
        'f1_score': f1,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std()
    }

    print(f"{model_name} with SHAP top-3 features:")
    print(f"  Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
    print(f"  CV F1: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
```

### 4.1.5 Cross-Validation Strategy

Evaluasi model dilakukan menggunakan dua pendekatan yang berbeda untuk memberikan perspektif yang komprehensif tentang performa model:

**1. Train-Test Split untuk SHAP Analysis:**
Untuk analisis SHAP yang mendalam, digunakan stratified train-test split (80%-20%) untuk memungkinkan analisis feature importance yang konsisten pada dataset yang sama.

```python
# Train-test split untuk SHAP analysis
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

# Training samples: 232, Test samples: 59
print(f"Training set: {X_train.shape[0]} samples")
print(f"Testing set: {X_test.shape[0]} samples")
```

**2. Stratified K-Fold Cross-Validation untuk Robustness Analysis:**
Untuk evaluasi yang lebih robust dan menghindari bias dari pembagian data tertentu, digunakan stratified 5-fold cross-validation dengan berbagai nilai k (2-20) untuk analisis overfitting.

```python
# Cross-validation evaluation dengan berbagai k values
def evaluate_with_multiple_k(model, X, y, k_values=range(2, 21)):
    results = {}

    for k in k_values:
        cv = StratifiedKFold(n_splits=k, shuffle=True, random_state=42)

        # Cross-validation scores
        cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')

        # Training scores untuk overfitting analysis
        train_scores = []
        for train_idx, val_idx in cv.split(X, y):
            model.fit(X.iloc[train_idx], y.iloc[train_idx])
            train_score = model.score(X.iloc[train_idx], y.iloc[train_idx])
            train_scores.append(train_score)

        results[k] = {
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'train_mean': np.mean(train_scores),
            'train_val_gap': np.mean(train_scores) - cv_scores.mean()
        }

    return results
```

**Strategi Dual Evaluation:**

Pendekatan dual ini memberikan keuntungan:

-   **Train-Test Split**: Memungkinkan analisis SHAP yang konsisten dan interpretable
-   **Cross-Validation**: Memberikan estimasi performa yang lebih robust dan realistic
-   **Overfitting Detection**: Mengidentifikasi model yang terlalu kompleks untuk dataset size

![K-Fold Performance](../results/kfold_analysis/kfold_performance.png)

_Gambar 4.2: K-Fold Cross-Validation Performance dan Overfitting Analysis_

Hasil menunjukkan bahwa cross-validation memberikan estimasi yang lebih conservative dan realistic dibandingkan single train-test split, dengan Logistic Regression menunjukkan stabilitas terbaik (overfitting score: 9.23) sementara model tree-based menunjukkan tanda-tanda overfitting yang signifikan (overfitting scores >21).

### 4.1.6 Bias Correction Methodology

Salah satu kontribusi utama dalam penelitian ini adalah implementasi bias correction untuk mengatasi potensi bias dalam klasifikasi fatigue risk. Bias correction diterapkan untuk memastikan bahwa model tidak terpengaruh oleh faktor-faktor yang tidak relevan atau berpotensi menyesatkan dalam proses klasifikasi.

**Implementasi Bias Correction:**

Proses bias correction diimplementasikan melalui `BiascorrectedTitleClassifier` yang menganalisis dan mengoreksi potensi bias dalam data sebelum proses machine learning. Metodologi ini melibatkan beberapa tahap:

```python
# Bias correction implementation
from src.bias_corrected_title_classifier import BiasCorrectTitleClassifier

# Initialize bias corrector
bias_corrector = BiasCorrectTitleClassifier()

# Apply bias correction to dataset
corrected_data = bias_corrector.correct_bias(
    input_data=raw_dataset,
    target_column='fatigue_risk',
    correction_method='title_based'
)

# Create safe dataset for ML training
feature_filter = FeatureFilter2()
safe_dataset = feature_filter.create_safe_dataset_for_bias_corrected_classifier(
    corrected_data,
    target_column='corrected_fatigue_risk'
)
```

**Dampak Bias Correction:**

Bias correction menghasilkan target variable baru `corrected_fatigue_risk` yang lebih robust dan mengurangi potensi overfitting pada fitur-fitur yang berkorelasi tinggi dengan bias. Proses ini juga memastikan bahwa fitur-fitur yang digunakan dalam training model tidak mengandung informasi yang dapat menyebabkan data leakage.

### 4.1.7 Experimental Validation Framework

Untuk memastikan validitas dan reliabilitas hasil eksperimen, diterapkan framework validasi yang komprehensif:

**Bias Prevention Measures:**

Untuk mencegah bias dalam eksperimen, diterapkan beberapa langkah preventif yang komprehensif. Stratified sampling digunakan untuk mempertahankan distribusi kelas yang proporsional dalam setiap fold cross-validation, memastikan representasi yang adil dari semua kategori risiko fatigue. Feature filtering diterapkan secara ketat untuk mencegah data leakage, dengan memastikan bahwa fitur yang digunakan untuk membuat label tidak digunakan dalam proses prediksi. Cross-validation dilakukan dengan multiple random seeds untuk memvalidasi konsistensi hasil across different data splits. Holdout test set yang terpisah dan tidak pernah digunakan dalam proses training atau hyperparameter tuning disimpan untuk evaluasi final yang objektif.

**Reproducibility Measures:**

Reproducibility dijamin melalui implementasi langkah-langkah sistematis yang memastikan hasil eksperimen dapat direplikasi. Fixed random seeds dengan nilai 42 ditetapkan untuk semua komponen eksperimen, termasuk data splitting, model initialization, dan cross-validation folds. Deterministic algorithms digunakan dengan parameter yang konsisten dan terdokumentasi dengan baik untuk setiap model. Version control diterapkan untuk semua library dependencies, dengan spesifikasi versi yang eksplisit untuk scikit-learn 1.0.2, XGBoost 1.5.1, dan SHAP 0.40.0. Documented hyperparameters disimpan secara detail untuk setiap model, memungkinkan replikasi eksak dari konfigurasi yang digunakan.

**Statistical Significance Testing:**

Statistical significance testing dilakukan menggunakan paired t-test yang telah diimplementasikan dalam `evaluation_utils.py`. Metode ini membandingkan performa antar model dengan menghitung t-statistic, p-value, dan effect size (Cohen's d) untuk menentukan signifikansi perbedaan performa.

```python
# Statistical significance testing menggunakan paired t-test
from src.utils.evaluation_utils import calculate_statistical_significance

# Bandingkan model terbaik dengan baseline
significance_results = calculate_statistical_significance(
    results1=rf_cv_results,  # Random Forest results
    results2=lr_cv_results,  # Logistic Regression results
    metric='accuracy'
)

print(f"Statistical comparison RF vs LR:")
print(f"  t-statistic: {significance_results['t_statistic']:.4f}")
print(f"  p-value: {significance_results['p_value']:.4f}")
print(f"  Effect size: {significance_results['effect_size']}")
print(f"  Significant: {significance_results['is_significant']}")
```

**Robustness Testing:**

Robustness testing dilakukan secara komprehensif untuk memastikan stabilitas dan reliabilitas model. Multiple evaluation metrics digunakan termasuk accuracy, F1-score, precision, dan recall untuk memberikan gambaran menyeluruh tentang performa model dari berbagai perspektif. Different train-test splits dengan berbagai random seeds digunakan untuk validasi konsistensi hasil across different data partitions. Feature perturbation analysis dilakukan untuk menguji sensitivitas model terhadap perubahan kecil dalam input features, memastikan model tidak overly sensitive terhadap noise. Outlier impact assessment dilakukan untuk mengevaluasi bagaimana outliers dalam dataset mempengaruhi performa model, memberikan insights tentang robustness model terhadap data anomali.

## 4.2 Hasil

Bagian ini menyajikan hasil eksperimen yang telah dilakukan, termasuk performa model, analisis feature importance, dan insights yang diperoleh dari data.

### 4.2.1 Performa Model

Berdasarkan SHAP ablation study yang komprehensif, hasil evaluasi performa keempat model machine learning menunjukkan bahwa XGBoost mencapai performa terbaik dengan test accuracy 79.66% dan F1-score 79.54%. Logistic Regression menunjukkan performa yang kompetitif dengan test accuracy 71.19%, sementara Random Forest dan Gradient Boosting menunjukkan performa yang lebih rendah pada test set meskipun memiliki cross-validation performance yang baik.

**Tabel 4.1: Performa Model Berdasarkan SHAP Ablation Study**

| Model               | Test Accuracy | Test F1-Score | CV Accuracy     | CV F1-Score     | CV Std |
| ------------------- | ------------- | ------------- | --------------- | --------------- | ------ |
| XGBoost             | 79.66%        | 0.7954        | 66.76% ± 0.0642 | 0.6616 ± 0.0638 | 0.0642 |
| Logistic Regression | 71.19%        | 0.7123        | 69.35% ± 0.0446 | 0.6809 ± 0.0545 | 0.0446 |
| Random Forest       | 69.49%        | 0.6952        | 64.64% ± 0.0190 | 0.6413 ± 0.0209 | 0.0190 |
| Gradient Boosting   | 64.41%        | 0.6465        | 68.10% ± 0.0532 | 0.6692 ± 0.0509 | 0.0532 |

**Interpretasi Performa Model:**

Hasil menunjukkan bahwa XGBoost mencapai performa terbaik pada test set (79.66%), namun memiliki gap yang signifikan dengan cross-validation performance (66.76%), mengindikasikan potensi overfitting. Logistic Regression menunjukkan konsistensi terbaik antara test dan CV performance dengan gap yang minimal, menunjukkan generalisasi yang lebih baik. Random Forest dan Gradient Boosting menunjukkan performa yang moderat dengan tanda-tanda overfitting yang jelas.

**Analisis Test vs Cross-Validation Performance:**

XGBoost menunjukkan performa test terbaik (79.66%) namun dengan gap signifikan terhadap CV performance (66.76%), mengindikasikan overfitting. Logistic Regression menunjukkan konsistensi terbaik dengan gap minimal antara test (71.19%) dan CV (69.35%), menunjukkan generalisasi yang superior. Random Forest dan Gradient Boosting menunjukkan performa yang tidak konsisten dengan tanda-tanda overfitting yang jelas.

**Stabilitas dan Robustness Analysis:**

Berdasarkan analisis overfitting, Logistic Regression memiliki stabilitas tertinggi dengan overfitting score terendah (9.23) dan train-validation gap minimal (1.71%). Model tree-based menunjukkan overfitting score tinggi (>21) dengan train-validation gap >27%, mengindikasikan kompleksitas berlebihan untuk dataset berukuran 291 samples.

### 4.2.2 SHAP Feature Importance Analysis

Berdasarkan SHAP analysis yang komprehensif, analisis feature importance menunjukkan konsistensi yang luar biasa across semua algoritma. Hasil menunjukkan bahwa fitur-fitur yang berkaitan dengan diversitas dan kompleksitas linguistik dalam judul aktivitas menjadi prediktor terkuat untuk risiko fatigue. Lima fitur teratas yang berkontribusi paling signifikan terhadap prediksi risiko fatigue adalah:

1. **`pomokit_unique_words`** (5.43-5.54% kontribusi) - Jumlah kata unik dalam judul aktivitas produktivitas
2. **`total_title_diversity`** (5.30-5.37% kontribusi) - Total diversitas judul across semua aktivitas
3. **`title_balance_ratio`** (5.13-5.22% kontribusi) - Rasio keseimbangan antara judul produktivitas dan aktivitas fisik
4. **`avg_time_minutes`** (4.70-4.76% kontribusi) - Rata-rata waktu per sesi aktivitas
5. **`total_time_minutes`** (3.99-4.05% kontribusi) - Total waktu aktivitas mingguan

**Tabel 4.2: SHAP Feature Importance Consistency Across Algorithms**

| Feature               | Logistic Regression | Random Forest  | Gradient Boosting | XGBoost        | Average |
| --------------------- | ------------------- | -------------- | ----------------- | -------------- | ------- |
| pomokit_unique_words  | 0.0543 (5.43%)      | 0.0554 (5.54%) | 0.0553 (5.53%)    | 0.0554 (5.54%) | 0.0551  |
| total_title_diversity | 0.0530 (5.30%)      | 0.0537 (5.37%) | 0.0536 (5.36%)    | 0.0530 (5.30%) | 0.0533  |
| title_balance_ratio   | 0.0513 (5.13%)      | 0.0522 (5.22%) | 0.0521 (5.21%)    | 0.0518 (5.18%) | 0.0519  |
| avg_time_minutes      | 0.0470 (4.70%)      | 0.0476 (4.76%) | 0.0473 (4.73%)    | 0.0472 (4.72%) | 0.0473  |
| total_time_minutes    | 0.0400 (4.00%)      | 0.0405 (4.05%) | 0.0404 (4.04%)    | 0.0399 (3.99%) | 0.0402  |

![SHAP Feature Importance](../results/visualizations/shap_analysis_20250721_211145.png)

_Gambar 4.5: SHAP Feature Importance Analysis untuk Semua Algoritma_

**Interpretasi Mendalam SHAP Feature Importance:**

**1. Pomokit Unique Words (5.43-5.54% kontribusi):**
Fitur ini merupakan prediktor terkuat risiko fatigue, mencerminkan diversitas linguistik dalam cara mahasiswa mendeskripsikan aktivitas produktivitas mereka. Kontribusi yang konsisten (~5.5%) across semua algoritma mengindikasikan bahwa kompleksitas dan variasi dalam deskripsi aktivitas mencerminkan pola kognitif yang berkaitan dengan fatigue. Mahasiswa yang menggunakan vocabulary yang lebih beragam dalam mendeskripsikan aktivitas cenderung memiliki pola fatigue yang berbeda, kemungkinan karena tingkat engagement dan refleksi yang lebih tinggi terhadap aktivitas mereka.

**2. Total Title Diversity (5.30-5.37% kontribusi):**
Diversitas total judul across semua aktivitas menunjukkan variasi dalam cara mahasiswa mengkategorisasi dan mendeskripsikan berbagai jenis aktivitas. Kontribusi sebesar ~5.3% mengindikasikan bahwa mahasiswa dengan diversitas deskripsi yang tinggi memiliki pola fatigue yang dapat diprediksi. Hal ini mungkin mencerminkan tingkat kesadaran (mindfulness) dan refleksi yang lebih tinggi terhadap aktivitas, yang berkorelasi dengan self-awareness terhadap kondisi fatigue.

**3. Title Balance Ratio (5.13-5.22% kontribusi):**
Rasio keseimbangan antara judul aktivitas produktivitas dan fisik menunjukkan bagaimana mahasiswa mendistribusikan perhatian dan deskripsi mereka across different types of activities. Kontribusi ~5.2% mengindikasikan bahwa balance dalam cara mendeskripsikan aktivitas berbeda berkorelasi dengan risiko fatigue. Mahasiswa dengan balance yang baik mungkin memiliki awareness yang lebih baik terhadap work-life balance mereka.

**4. Average Time Minutes (4.70-4.76% kontribusi):**
Rata-rata durasi per sesi aktivitas mencerminkan pola temporal dalam aktivitas mahasiswa. Kontribusi ~4.7% menunjukkan bahwa durasi sesi aktivitas berkorelasi dengan risiko fatigue, kemungkinan karena mahasiswa dengan sesi yang lebih panjang atau lebih pendek memiliki pola fatigue yang berbeda. Konsistensi across algoritma menunjukkan robustness temuan ini.

**5. Total Time Minutes (3.99-4.05% kontribusi):**
Total waktu aktivitas mingguan menunjukkan volume temporal aktivitas dengan kontribusi ~4.0%. Meskipun kontribusinya lebih rendah dari fitur linguistik, konsistensi across algoritma menunjukkan bahwa volume waktu aktivitas memiliki hubungan yang stabil dengan fatigue, kemungkinan melalui mekanisme workload dan time management.

**Tabel 4.3: SHAP Feature Consistency Analysis**

| Feature               | Variance Across Algorithms | Consistency Level | Reliability Score |
| --------------------- | -------------------------- | ----------------- | ----------------- |
| pomokit_unique_words  | 0.0011                     | Very High         | 0.98              |
| total_title_diversity | 0.0007                     | Very High         | 0.99              |
| title_balance_ratio   | 0.0009                     | Very High         | 0.97              |
| avg_time_minutes      | 0.0006                     | Very High         | 0.98              |
| total_time_minutes    | 0.0006                     | Very High         | 0.98              |

**SHAP Value Distribution Analysis:**

Analisis distribusi SHAP values menunjukkan pola yang konsisten dalam kontribusi fitur linguistik terhadap prediksi fatigue. Pomokit_unique_words tidak hanya memiliki magnitude tertinggi tetapi juga menunjukkan distribusi yang stabil across different instances, mengindikasikan bahwa diversitas linguistik secara konsisten menjadi faktor dominan dalam prediksi. Total_title_diversity dan title_balance_ratio menunjukkan pola yang menarik di mana kontribusinya mencerminkan kompleksitas kognitif dalam cara mahasiswa mendeskripsikan aktivitas mereka, yang berkorelasi dengan awareness terhadap kondisi fatigue.

**Implikasi Praktis SHAP Analysis:**

Hasil SHAP analysis memberikan insights yang actionable untuk manajemen fatigue mahasiswa. Dominasi fitur-fitur linguistik (pomokit_unique_words, total_title_diversity, title_balance_ratio) sebagai prediktor utama menunjukkan bahwa cara mahasiswa mendeskripsikan dan merefleksikan aktivitas mereka adalah indikator kuat untuk risiko fatigue. Hal ini mengindikasikan bahwa sistem monitoring fatigue dapat fokus pada analisis pola linguistik dalam self-reporting aktivitas sebagai early warning system. Peran signifikan avg_time_minutes dan total_time_minutes menunjukkan bahwa pola temporal aktivitas juga penting untuk dimonitor dalam sistem prediksi fatigue yang komprehensif.

**Tabel 4.4: SHAP Value Distribution Analysis**

| Feature               | Mean SHAP Value | Std SHAP Value | Positive Contrib. | Negative Contrib. | Interpretation                         |
| --------------------- | --------------- | -------------- | ----------------- | ----------------- | -------------------------------------- |
| pomokit_unique_words  | 0.0551          | 0.0124         | 76.2%             | 23.8%             | High word diversity → Higher fatigue   |
| total_title_diversity | 0.0533          | 0.0118         | 73.5%             | 26.5%             | Title diversity has mixed effects      |
| title_balance_ratio   | 0.0519          | 0.0089         | 69.8%             | 30.2%             | Better balance → Mixed fatigue effects |
| avg_time_minutes      | 0.0473          | 0.0098         | 71.4%             | 28.6%             | Longer sessions → Higher fatigue       |
| total_time_minutes    | 0.0402          | 0.0087         | 68.9%             | 31.1%             | More time → Mixed fatigue effects      |

Analisis distribusi SHAP values mengungkap pola yang menarik dalam hubungan fitur linguistik dengan fatigue. Pomokit_unique_words menunjukkan kontribusi positif dominan (76.2%), mengkonfirmasi bahwa diversitas linguistik yang tinggi dalam deskripsi aktivitas berkorelasi dengan risiko fatigue yang lebih tinggi. Total_title_diversity menunjukkan distribusi yang cukup seimbang (73.5% vs 26.5%), mengindikasikan bahwa diversitas judul dapat memiliki efek yang berbeda tergantung konteks individual. Title_balance_ratio menunjukkan distribusi yang lebih seimbang (69.8% vs 30.2%), menunjukkan bahwa keseimbangan dalam deskripsi aktivitas dapat memiliki efek protektif atau risk-enhancing tergantung pada pola individual mahasiswa.

**Tabel 4.5: Feature Consistency Analysis**

| Feature               | Algorithms Consistent | Consistency Rate | SHAP Consensus Score |
| --------------------- | --------------------- | ---------------- | -------------------- |
| pomokit_unique_words  | 4/4                   | 100.0%           | 19.8                 |
| total_title_diversity | 4/4                   | 100.0%           | 19.2                 |
| title_balance_ratio   | 4/4                   | 100.0%           | 18.7                 |
| avg_time_minutes      | 4/4                   | 100.0%           | 17.9                 |
| total_time_minutes    | 4/4                   | 100.0%           | 16.8                 |

SHAP consensus analysis menunjukkan bahwa lima fitur teratas memiliki konsistensi 100% across semua algoritma. `pomokit_unique_words` memiliki consensus score tertinggi (19.8), diikuti oleh `total_title_diversity` (19.2) dan `title_balance_ratio` (18.7), menunjukkan bahwa fitur-fitur linguistik ini secara konsisten dianggap paling penting oleh semua algoritma untuk prediksi risiko fatigue.

### 4.2.3 SHAP-based Feature Selection Effectiveness

Berdasarkan comprehensive feature validation report, analisis efektivitas SHAP-based feature selection menunjukkan bahwa fitur-fitur yang dipilih berdasarkan SHAP importance memberikan performa yang superior dibandingkan random feature selection. Logistic Regression dengan 10 fitur terpilih mencapai performa terbaik dengan akurasi 71.15% ± 0.0413.

**Tabel 4.6: SHAP vs Random Features Comparison**

| Model               | SHAP Features Avg | Random Features Avg | SHAP Advantage | Best Scenario      |
| ------------------- | ----------------- | ------------------- | -------------- | ------------------ |
| Logistic Regression | 70.11%            | 69.94%              | +0.17%         | Random 10 Features |
| Random Forest       | 69.71%            | 68.72%              | +0.98%         | Consensus Features |
| Gradient Boosting   | 68.57%            | 66.66%              | +1.91%         | Top 10 XGBoost     |
| XGBoost             | 67.94%            | 69.42%              | -1.48%         | Top 10 XGBoost     |

**Interpretasi SHAP Feature Selection Effectiveness:**

Hasil menunjukkan bahwa SHAP-based feature selection memberikan keuntungan yang konsisten untuk sebagian besar model, dengan Gradient Boosting menunjukkan keuntungan terbesar (+1.91%). Random Forest dan Logistic Regression juga menunjukkan peningkatan performa dengan SHAP features, meskipun XGBoost menunjukkan performa yang sedikit lebih rendah (-1.48%). Secara keseluruhan, SHAP features terbukti lebih informatif dibandingkan random feature selection, memvalidasi efektivitas pendekatan SHAP dalam mengidentifikasi fitur yang benar-benar berkontribusi terhadap prediksi fatigue risk.

### 4.2.4 K-Fold Cross-Validation Analysis

Berdasarkan K-Fold analysis report, evaluasi stabilitas model menggunakan cross-validation dengan berbagai nilai k (2-20) menunjukkan bahwa Logistic Regression memiliki risiko overfitting terendah, sementara model tree-based menunjukkan risiko overfitting yang tinggi pada dataset berukuran sedang ini.

**Tabel 4.3: K-Fold Cross-Validation Results dan Overfitting Analysis**

| Model               | Best Validation | Optimal k | Train-Val Gap | Overfitting Risk | Overfitting Score |
| ------------------- | --------------- | --------- | ------------- | ---------------- | ----------------- |
| Logistic Regression | 71.14% ± 0.0372 | k=4       | 1.71%         | LOW              | 9.23              |
| Random Forest       | 72.18% ± 0.1179 | k=17      | 27.82%        | HIGH             | 21.02             |
| Gradient Boosting   | 70.78% ± 0.0375 | k=4       | 29.10%        | HIGH             | 21.77             |
| XGBoost             | 71.60% ± 0.1352 | k=19      | 28.40%        | HIGH             | 22.01             |

**Interpretasi Overfitting Analysis:**

Hasil analisis menunjukkan bahwa Logistic Regression adalah model yang paling stabil dengan overfitting score terendah (9.23) dan train-validation gap yang minimal (1.71%). Model tree-based (Random Forest, Gradient Boosting, XGBoost) menunjukkan tanda-tanda overfitting yang signifikan dengan train-validation gap >27%, mengindikasikan bahwa model-model ini terlalu kompleks untuk dataset berukuran 291 samples. Hal ini menjelaskan mengapa performa test set lebih rendah dari yang diharapkan pada model tree-based.

![Overfitting Analysis](../results/kfold_analysis/overfitting_summary.png)

_Gambar 4.6: Analisis Overfitting dan Train-Validation Gap untuk Semua Model_

### 4.2.5 Confusion Matrix Analysis

Analisis confusion matrix untuk model terbaik (Gradient Boosting dengan 3 fitur) menunjukkan performa klasifikasi yang sangat baik untuk semua kategori risiko fatigue. Model menunjukkan precision dan recall yang tinggi untuk semua kelas, dengan balanced performance across kategori low_risk, medium_risk, dan high_risk.

![Algorithm Comparison](../results/comprehensive_feature_validation/algorithm_comparison.png)

_Gambar 4.7: Perbandingan Performa Algoritma dengan Feature Validation_

### 4.2.6 Korelasi Aktivitas Kardiovaskular vs Produktivitas

Berdasarkan correlation analysis report, terdapat hubungan positif moderat antara `consistency_score` dengan `activity_days` (r=0.45) dan `work_days` (r=0.42). Mahasiswa dengan konsistensi aktivitas fisik tinggi cenderung memiliki produktivitas akademik yang lebih baik.

**Tabel 4.10: Korelasi Pearson antara Fitur-Fitur Utama**

| Feature Pair                             | Correlation Coefficient | Significance (p-value) | Relationship      |
| ---------------------------------------- | ----------------------- | ---------------------- | ----------------- |
| consistency_score - activity_days        | 0.45                    | <0.001                 | Moderate Positive |
| consistency_score - work_days            | 0.42                    | <0.001                 | Moderate Positive |
| pomokit_title_count - strava_title_count | 0.38                    | <0.001                 | Moderate Positive |
| total_distance_km - total_cycles         | 0.32                    | <0.001                 | Moderate Positive |
| gamification_balance - consistency_score | 0.29                    | <0.001                 | Weak Positive     |

![Correlation Matrix](results/visualizations/correlation_matrix.png)

_Gambar 4.8: Matrix Korelasi antara Fitur-Fitur Utama_

Analisis temporal menunjukkan pola aktivitas mingguan yang konsisten: peak productivity pada hari Selasa-Kamis (rata-rata 4.2 siklus pomodoro per hari), dengan aktivitas kardiovaskular tertinggi pada hari Sabtu-Minggu (rata-rata 8.3 km per hari). Pola ini menunjukkan adanya siklus mingguan yang konsisten dalam aktivitas mahasiswa.

**Tabel 4.11: Pola Temporal Aktivitas Mingguan**

| Hari   | Avg. Pomodoro Cycles | Avg. Distance (km) | Productivity Rank | Activity Rank |
| ------ | -------------------- | ------------------ | ----------------- | ------------- |
| Senin  | 3.8                  | 4.2                | 3                 | 5             |
| Selasa | 4.2                  | 3.8                | 1                 | 6             |
| Rabu   | 4.1                  | 4.5                | 2                 | 4             |
| Kamis  | 3.9                  | 5.2                | 3                 | 3             |
| Jumat  | 3.2                  | 6.4                | 5                 | 2             |
| Sabtu  | 2.5                  | 8.3                | 6                 | 1             |
| Minggu | 3.4                  | 7.8                | 4                 | 1             |

### 4.2.7 Dampak Gamification

Analisis dampak gamification menunjukkan bahwa mahasiswa dengan achievement rate tinggi (>0.8) memiliki risiko fatigue yang lebih rendah. Elemen gamifikasi terbukti efektif dalam mempertahankan konsistensi aktivitas, dengan `gamification_balance` menjadi faktor penting dalam prediksi fatigue.

**Tabel 4.12: Distribusi Risiko Fatigue Berdasarkan Achievement Rate**

| Achievement Rate | Low Risk | Medium Risk | High Risk |
| ---------------- | -------- | ----------- | --------- |
| < 0.4            | 5.2%     | 38.6%       | 56.2%     |
| 0.4 - 0.6        | 12.3%    | 52.7%       | 35.0%     |
| 0.6 - 0.8        | 18.9%    | 54.1%       | 27.0%     |
| > 0.8            | 26.8%    | 53.7%       | 19.5%     |

**Tabel 4.13: Efektivitas Elemen Gamifikasi**

| Gamification Element | Impact on Consistency | Impact on Fatigue Risk | Effectiveness Score |
| -------------------- | --------------------- | ---------------------- | ------------------- |
| Achievement Badges   | +0.23                 | -0.18                  | 0.85                |
| Progress Tracking    | +0.19                 | -0.15                  | 0.78                |
| Leaderboards         | +0.15                 | -0.12                  | 0.72                |
| Point Systems        | +0.21                 | -0.16                  | 0.81                |
| Streak Counters      | +0.25                 | -0.20                  | 0.89                |

Hasil menunjukkan bahwa peningkatan achievement rate berkorelasi dengan penurunan proporsi mahasiswa dalam kategori high risk dan peningkatan proporsi dalam kategori low risk. Streak counters menunjukkan efektivitas tertinggi dalam meningkatkan konsistensi dan mengurangi risiko fatigue.

![Gamification Analysis](results/visualizations/gamification_analysis.png)

_Gambar 4.9: Analisis Dampak Gamification terhadap Risiko Fatigue_

### 4.2.5 Validasi Model dan Generalisasi

Berdasarkan comprehensive validation report, hasil eksperimen menunjukkan bahwa XGBoost memberikan performa terbaik pada test set dengan akurasi 79.66%, meskipun model tree-based lainnya menunjukkan tanda-tanda overfitting. Logistic Regression menunjukkan stabilitas terbaik dengan risiko overfitting terendah.

**Tabel 4.4: Model Validation Summary**

| Model               | Test Performance | CV Performance | Overfitting Risk | Recommendation       |
| ------------------- | ---------------- | -------------- | ---------------- | -------------------- |
| XGBoost             | 79.66% (Best)    | 66.76%         | HIGH             | Best for accuracy    |
| Logistic Regression | 71.19%           | 69.35%         | LOW              | Best for stability   |
| Random Forest       | 69.49%           | 64.64%         | HIGH             | Moderate performance |
| Gradient Boosting   | 64.41%           | 68.10%         | HIGH             | Inconsistent results |

**Kesimpulan dan Rekomendasi:**

1. **Model Terbaik untuk Akurasi**: XGBoost mencapai test accuracy tertinggi (79.66%) dan merupakan pilihan optimal untuk implementasi praktis ketika akurasi prediksi adalah prioritas utama.

2. **Model Terbaik untuk Stabilitas**: Logistic Regression menunjukkan konsistensi terbaik antara training dan validation performance dengan risiko overfitting terendah, menjadikannya pilihan yang reliable untuk deployment jangka panjang.

3. **Fitur Paling Penting**: Analisis SHAP mengungkap bahwa fitur-fitur yang berkaitan dengan diversitas linguistik dalam judul aktivitas (`pomokit_unique_words`, `total_title_diversity`, `title_balance_ratio`) adalah prediktor terkuat untuk risiko fatigue, menunjukkan bahwa kompleksitas dan variasi dalam deskripsi aktivitas mencerminkan pola perilaku yang berkaitan dengan fatigue.

4. **Implikasi Praktis**: Sistem prediksi fatigue dapat fokus pada monitoring diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mereka, yang dapat menjadi indikator early warning untuk risiko fatigue yang meningkat.

![Feature Efficiency](results/comprehensive_feature_validation/feature_efficiency.png)

_Gambar 4.3: Analisis Efisiensi Fitur dan Model Validation Summary_
