# ABSTRACT

Fatigue is a serious problem that affects students' mental health and academic performance. This research aims to develop a student fatigue risk classification system using SHAP feature selection on cardiovascular and productivity activity data, focusing on identifying significant factors, evaluating machine learning model effectiveness, and validating title-only analysis approach. The research methodology employed four machine learning algorithms (Logistic Regression, Random Forest, Gradient Boosting, and XGBoost) on a dataset of 291 weekly student observations with 20 features encompassing productivity, physical activity, temporal, and linguistic aspects. Implementation of a bias correction framework through `BiasCorrectTitleClassifier` generated a more robust target variable `corrected_fatigue_risk`, with SHAP utilized for feature selection and model interpretability through a dual evaluation strategy. Results demonstrate that linguistic features dominate fatigue risk prediction, with pomokit_unique_words (5.54%), total_title_diversity (5.33%), and title_balance_ratio (5.19%) as the strongest predictors. XGBoost achieved the best performance with test accuracy of 79.66% but showed high overfitting risk, while Logistic Regression demonstrated superior stability with consistency between test accuracy (71.19%) and CV performance (69.35%). Title-only analysis proved effective with linguistic features contributing 15.06% of feature importance. Research contributions include identification of linguistic features as primary fatigue risk predictors, development of bias correction framework, and validation of title-only analysis effectiveness for practical and non-intrusive fatigue monitoring, with SHAP feature selection proving superior to random selection (improvements of 0.17%-1.91%).

**Keywords:** fatigue classification, SHAP feature selection, machine learning, linguistic features, title-only analysis, bias correction
