# BAB V

# KESIMPULAN

## 5.1 Kesimpulan Masalah: Faktor-faktor Signifikan dalam Prediksi Risiko Fatigue

Penelitian ini berhasil menjawab pertanyaan penelitian pertama mengenai "Faktor-faktor apa saja yang paling signifikan dalam memprediksi risiko fatigue pada mahasiswa, dan bagaimana kontribusi individual setiap fitur dapat divalidasi melalui ablation study" melalui analisis SHAP yang komprehensif.

Berdasarkan SHAP ablation study, faktor-faktor yang paling signifikan dalam memprediksi risiko fatigue adalah fitur-fitur linguistik: pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%). Temuan mengejutkan bahwa fitur linguistik lebih dominan dibandingkan fitur temporal seperti avg_time_minutes (4.73%) dan total_time_minutes (4.02%), mengindikasikan bahwa cara mahasiswa mendeskripsikan aktivitas mengandung informasi yang lebih kaya tentang kondisi psikologis dan kognitif yang berkaitan dengan fatigue.

Kontribusi individual setiap fitur berhasil divalidasi melalui systematic ablation study dengan konsistensi 100% across semua algoritma dan variance yang sangat rendah (0.0006-0.0011). SHAP-based feature selection terbukti superior dibandingkan random selection dengan improvement konsisten pada sebagian besar algoritma. Dominasi fitur linguistik mengungkap bahwa diversitas dan kompleksitas dalam cara mahasiswa mendeskripsikan aktivitas mencerminkan pola kognitif yang berkaitan dengan kondisi fatigue, memberikan implikasi praktis untuk pengembangan sistem monitoring yang fokus pada analisis pola linguistik.

## 5.2 Kesimpulan Metode: Efektivitas Model Machine Learning

Penelitian ini berhasil menjawab pertanyaan penelitian kedua mengenai "Bagaimana efektivitas model machine learning dalam mengklasifikasikan tingkat risiko fatigue berdasarkan data aktivitas kardiovaskular dan produktivitas" melalui evaluasi komprehensif menggunakan dual evaluation strategy.

Efektivitas model machine learning menunjukkan hasil yang realistis dengan XGBoost mencapai performa terbaik (test accuracy 79.66%, F1-score 79.54%) namun menunjukkan risiko overfitting tinggi (CV accuracy 66.76%, overfitting score 22.01). Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test accuracy (71.19%) dan CV performance (69.35%) serta overfitting score terendah (9.23), menjadikannya pilihan yang lebih reliable untuk deployment jangka panjang.

Evaluasi mengungkap bahwa untuk dataset berukuran sedang (291 samples) dengan distribusi kelas tidak seimbang, model linear memberikan generalisasi yang lebih baik dibandingkan model kompleks. Model tree-based menunjukkan overfitting signifikan dengan overfitting score >21 dan train-validation gap >27%. Dual evaluation strategy terbukti efektif dalam mengidentifikasi model yang truly effective, dengan perbedaan signifikan antara train-test split (79.66%) dan cross-validation (66.76%) mengkonfirmasi pentingnya evaluasi komprehensif.

Penelitian ini mengembangkan metodologi komprehensif meliputi Bias Correction Framework, SHAP-based feature selection untuk interpretabilitas superior, dan multi-modal data integration yang menghasilkan 20 fitur informatif dari berbagai modalitas data aktivitas mahasiswa.

## 5.3 Kesimpulan Eksperimen: Analisis Berbasis Judul Aktivitas

Penelitian ini berhasil menjawab pertanyaan penelitian ketiga mengenai "Apakah analisis berbasis judul aktivitas (title-only analysis) dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap" dengan hasil yang positif.

Analisis berbasis judul aktivitas terbukti dapat memberikan prediksi fatigue yang akurat tanpa memerlukan data kuantitatif lengkap. Fitur-fitur linguistik yang diekstrak dari judul aktivitas (pomokit_unique_words, total_title_diversity, title_balance_ratio) mendominasi ranking feature importance dengan kontribusi total 15.06%, lebih tinggi dibandingkan fitur kuantitatif lainnya. Hal ini memvalidasi bahwa informasi dalam judul aktivitas sudah cukup untuk prediksi fatigue yang akurat.

Title-only analysis memiliki keunggulan praktis karena lebih sederhana, less intrusive, dan mudah diimplementasikan dalam sistem monitoring yang existing. Pendekatan ini juga memberikan insights tentang aspek psikologis mahasiswa melalui pola linguistik dalam deskripsi aktivitas yang berkorelasi dengan kondisi fatigue.

Penelitian ini memberikan kontribusi signifikan berupa identifikasi fitur linguistik sebagai prediktor utama fatigue, pengembangan framework bias correction dan dual evaluation strategy, serta rekomendasi praktis untuk implementasi sistem monitoring fatigue yang efektif dan dapat diandalkan.

Secara keseluruhan, penelitian ini berhasil mengembangkan metodologi yang robust untuk prediksi risiko fatigue mahasiswa dengan akurasi yang realistis dan interpretabilitas yang tinggi, memberikan foundation yang solid untuk pengembangan sistem monitoring kesehatan mahasiswa yang lebih efektif dan dapat diimplementasikan dalam praktik.
