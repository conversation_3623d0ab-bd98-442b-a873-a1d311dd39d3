# ABSTRAK

Fatigue merupakan masalah serius yang mempengaruhi kesehatan mental dan performa akademik mahasiswa. Penelitian ini bertujuan untuk mengembangkan sistem klasifikasi risiko fatigue mahasiswa menggunakan SHAP feature selection pada data aktivitas kardiovaskular dan produktivitas, dengan fokus pada identifikasi faktor-faktor signifikan, evaluasi efektivitas model machine learning, dan validasi pendekatan analisis berbasis judul aktivitas. Metodologi penelitian menggunakan empat algoritma machine learning (Logistic Regression, Random Forest, Gradient Boosting, dan XGBoost) pada dataset 291 observasi mingguan mahasiswa dengan 20 fitur yang mencakup aspek produktivitas, aktivitas fisik, temporal, dan linguistik. Implementasi bias correction framework melalui `BiasCorrectTitleClassifier` menghasilkan target variable `corrected_fatigue_risk` yang lebih robust, dengan SHAP digunakan untuk feature selection dan interpretabilitas model melalui dual evaluation strategy. Hasil penelitian menunjukkan bahwa fitur-fitur linguistik mendominasi prediksi risiko fatigue, dengan pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%) sebagai prediktor terkuat. XGBoost mencapai performa terbaik dengan test accuracy 79.66% namun menunjukkan risiko overfitting tinggi, sementara Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test accuracy (71.19%) dan CV performance (69.35%). Analisis berbasis judul aktivitas terbukti efektif dengan fitur linguistik berkontribusi 15.06% dari feature importance. Kontribusi penelitian meliputi identifikasi fitur linguistik sebagai prediktor utama fatigue risk, pengembangan framework bias correction, dan validasi efektivitas title-only analysis untuk monitoring fatigue yang praktis dan non-intrusive, dengan SHAP feature selection terbukti superior dibandingkan random selection (improvement 0.17%-1.91%).

**Kata kunci:** klasifikasi fatigue, SHAP feature selection, machine learning, fitur linguistik, title-only analysis, bias correction
