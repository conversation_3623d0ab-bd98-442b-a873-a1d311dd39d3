# ABSTRAK

Fatigue merupakan masalah serius yang mempengaruhi kesehatan mental dan performa akademik mahasiswa. Penelitian ini bertujuan untuk mengembangkan sistem klasifikasi risiko fatigue mahasiswa menggunakan SHAP feature selection pada data aktivitas kardiovaskular dan produktivitas, dengan fokus pada identifikasi faktor-faktor signifikan, evaluasi efektivitas model machine learning, dan validasi pendekatan analisis berbasis judul aktivitas untuk prediksi yang akurat tanpa memerlukan data kuantitatif lengkap.

Metodologi penelitian menggunakan pendekatan machine learning dengan empat algoritma: Logistic Regression, Random Forest, Gradient Boosting, dan XGBoost. Dataset terdiri dari 291 observasi mingguan mahasiswa dengan 20 fitur yang mencakup aspek produktivitas, aktivitas fisik, temporal, dan linguistik. Implementasi bias correction framework melalui `BiasCorrectTitleClassifier` menghasilkan target variable `corrected_fatigue_risk` yang lebih robust. SHAP (SHapley Additive exPlanations) digunakan untuk feature selection dan interpretabilitas model, dengan dual evaluation strategy yang mengombinasikan train-test split untuk analisis SHAP dan k-fold cross-validation untuk robustness evaluation.

Hasil penelitian menunjukkan bahwa fitur-fitur linguistik mendominasi prediksi risiko fatigue. SHAP ablation study mengidentifikasi pomokit_unique_words (5.54%), total_title_diversity (5.33%), dan title_balance_ratio (5.19%) sebagai prediktor terkuat, mengindikasikan bahwa cara mahasiswa mendeskripsikan aktivitas mengandung informasi yang lebih kaya tentang kondisi psikologis dibandingkan fitur kuantitatif. XGBoost mencapai performa terbaik dengan test accuracy 79.66% dan F1-score 79.54%, namun menunjukkan risiko overfitting tinggi (CV accuracy 66.76%). Logistic Regression menunjukkan stabilitas superior dengan konsistensi antara test accuracy (71.19%) dan CV performance (69.35%) serta overfitting score terendah (9.23%). Analisis berbasis judul aktivitas (title-only analysis) terbukti dapat memberikan prediksi yang akurat dengan fitur linguistik berkontribusi total 15.06% dari feature importance, lebih tinggi dibandingkan fitur kuantitatif lainnya.

Kontribusi penelitian meliputi identifikasi fitur linguistik sebagai prediktor utama fatigue risk, pengembangan framework bias correction dan dual evaluation strategy, serta validasi efektivitas title-only analysis untuk monitoring fatigue yang praktis dan non-intrusive. SHAP feature selection terbukti superior dibandingkan random selection dengan improvement 0.17%-1.91% pada sebagian besar algoritma. Penelitian ini memberikan foundation untuk pengembangan sistem monitoring kesehatan mahasiswa yang fokus pada analisis pola linguistik sebagai early warning system.

**Kata kunci:** klasifikasi fatigue, SHAP feature selection, machine learning, fitur linguistik, title-only analysis, bias correction
